import { WebSocketServer } from 'ws';
import protobuf from 'protobufjs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import net from 'net';
import EncryptionUtils from '../encryption/EncryptionUtils.js';

// 检查端口是否可用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(port, () => {
      server.once('close', () => resolve(true));
      server.close();
    });
    server.on('error', () => resolve(false));
  });
}

// 找到可用端口
async function findAvailablePort(startPort = 8080) {
  for (let port = startPort; port < startPort + 10; port++) {
    if (await checkPort(port)) {
      return port;
    }
  }
  throw new Error('No available port found');
}

class CargoWebSocketServer {
  constructor() {
    this.clients = new Map();
    this.encryptionEnabled = process.env.ENCRYPTION_ENABLED === 'true' || false;
    this.encryptionUtils = this.encryptionEnabled ? new EncryptionUtils() : null;

    console.log(`[INFO] 加密模式: ${this.encryptionEnabled ? '启用' : '禁用'}`);

    this.initProtobuf().then(() => {
      this.setupWebSocketServer();
    }).catch(error => {
      console.error('Failed to initialize server:', error);
    });
  }

  async initProtobuf() {
    try {
      const __filename = fileURLToPath(import.meta.url);
      const __dirname = dirname(__filename);
      const protoPath = join(__dirname, '../../proto/cargo.proto');
      
      const root = await protobuf.load(protoPath);
      
      this.WebSocketMessage = root.lookupType('cargo.WebSocketMessage');
      this.MessageType = root.lookupEnum('cargo.MessageType');
      this.CargoData = root.lookupType('cargo.CargoData');
      this.CargoBatch = root.lookupType('cargo.CargoBatch');
      this.CommonData = root.lookupType('cargo.CommonData');
      this.CommonStatus = root.lookupType('cargo.CommonStatus');
      this.DataField = root.lookupType('cargo.DataField');
      
      console.log('Protobuf initialized successfully');
    } catch (error) {
      console.error('Failed to initialize protobuf:', error);
      throw error;
    }
  }

  async setupWebSocketServer() {
    try {
      const port = await findAvailablePort(8080);
      
      this.server = new WebSocketServer({ port });
      
      this.server.on('connection', (ws, req) => {
        const clientId = this.generateClientId();
        console.log(`Client ${clientId} connected`);
        
        this.clients.set(clientId, {
          ws,
          subscriptions: new Set(),
          lastHeartbeat: Date.now()
        });

        ws.on('message', (data) => {
          this.handleMessage(clientId, data);
        });

        ws.on('close', () => {
          console.log(`Client ${clientId} disconnected`);
          this.clients.delete(clientId);
        });

        ws.on('error', (error) => {
          console.error(`Client ${clientId} error:`, error);
        });
      });

      console.log(`WebSocket server running on port ${port}`);
      console.log('Server ready - no data simulation, expecting real backend connection');
      
    } catch (error) {
      console.error('Failed to setup WebSocket server:', error);
    }
  }

  handleMessage(clientId, data) {
    try {
      console.log(`[DEBUG] 收到客户端 ${clientId} 的消息，大小: ${data.length} bytes`);
      console.log(`[DEBUG] 前20字节: ${Array.from(data.slice(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`);
      
      const wsMsg = this.WebSocketMessage.decode(data);
      const client = this.clients.get(clientId);
      
      console.log(`[DEBUG] 解析成功 - 消息类型: ${wsMsg.type}, 消息ID: ${wsMsg.messageId}`);
      
      switch (wsMsg.type) {
        case this.MessageType.values.HEARTBEAT:
          console.log(`[DEBUG] 客户端 ${clientId} 发送心跳`);
          client.lastHeartbeat = Date.now();
          this.sendHeartbeat(clientId);
          break;
        case this.MessageType.values.SUBSCRIBE:
          console.log(`[DEBUG] 客户端 ${clientId} 请求订阅数据`);
          client.subscriptions.add('data_stream');
          // 这里可以开始向该客户端推送数据
          this.sendSubscribeConfirmation(clientId);
          break;
        default:
          console.log(`[DEBUG] 未知消息类型: ${wsMsg.type}`);
      }
    } catch (error) {
      console.error(`[ERROR] 解析消息失败:`, error.message);
      console.log(`[DEBUG] 尝试作为文本消息处理...`);
      try {
        const textMessage = data.toString('utf8');
        console.log(`[DEBUG] 文本消息内容: ${textMessage}`);
      } catch (textError) {
        console.error(`[ERROR] 文本解析也失败:`, textError.message);
      }
    }
  }

  sendHeartbeat(clientId) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== 1) return;

    const wsMsg = this.WebSocketMessage.create({
      type: this.MessageType.values.HEARTBEAT,
      payload: new Uint8Array(),
      timestamp: Date.now(),
      messageId: this.generateMessageId()
    });

    const buffer = this.WebSocketMessage.encode(wsMsg).finish();
    this.sendEncryptedMessage(client.ws, buffer);
  }

  generateClientId() {
    return Math.random().toString(36).substr(2, 9);
  }

  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  /**
   * 发送加密消息（如果启用加密）
   */
  sendEncryptedMessage(ws, data) {
    try {
      let finalData = data;

      if (this.encryptionEnabled && this.encryptionUtils) {
        finalData = this.encryptionUtils.encrypt(data);
        console.log(`[DEBUG] 消息已加密，原始大小: ${data.length}, 加密后大小: ${finalData.length}`);
      }

      ws.send(finalData);
    } catch (error) {
      console.error('[ERROR] 发送加密消息失败:', error);
    }
  }

  sendSubscribeConfirmation(clientId) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== 1) return;

    const confirmMsg = this.WebSocketMessage.create({
      type: this.MessageType.values.MESSAGE,
      payload: Buffer.from(JSON.stringify({
        status: 'subscribed',
        message: 'Successfully subscribed to data stream'
      })),
      timestamp: Date.now(),
      messageId: this.generateMessageId()
    });

    const buffer = this.WebSocketMessage.encode(confirmMsg).finish();
    this.sendEncryptedMessage(client.ws, buffer);
    console.log(`[DEBUG] 向客户端 ${clientId} 发送订阅确认`);
  }

  // 手动推送base64数据的方法（用于测试）
  sendBase64Data(base64String) {
    if (this.clients.size === 0) {
      console.log('No clients connected');
      return;
    }

    try {
      const binaryString = atob(base64String);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      this.clients.forEach((client, clientId) => {
        if (client.ws.readyState === 1) {
          this.sendEncryptedMessage(client.ws, bytes.buffer);
        }
      });

      console.log(`Sent base64 data to ${this.clients.size} clients`);
    } catch (error) {
      console.error('Error sending base64 data:', error);
    }
  }
}

new CargoWebSocketServer();



