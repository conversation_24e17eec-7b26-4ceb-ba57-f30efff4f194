using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace CargoWebSocketClient
{
    public partial class MainWindow : Window
    {
        private CargoWebSocketClient? _client;
        private ObservableCollection<DataItem> _dataItems;
        private DateTime _connectTime;
        private int _totalMessages = 0;
        private DataItem? _selectedDataItem; // 当前选中的数据项
        private bool _isDetailModalOpen = false; // 弹框状态

        public MainWindow()
        {
            InitializeComponent();
            _dataItems = new ObservableCollection<DataItem>();
            DataListView.ItemsSource = _dataItems;
            UpdateConnectionStatus(false);
            
            // 添加场景选择变化事件
            SceneComboBox.SelectionChanged += SceneComboBox_SelectionChanged;
            
            // 添加键盘事件监听
            this.KeyDown += MainWindow_KeyDown;
            
            // 添加ListView选择事件
            DataListView.SelectionChanged += DataListView_SelectionChanged;
            
            // 确保窗口可以接收键盘焦点
            this.Focusable = true;
        }

        private async void ConnectButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string serverUrl = ServerUrlTextBox.Text.Trim();
                Console.WriteLine($"[DEBUG] 用户输入的服务器地址: {serverUrl}");
                
                if (string.IsNullOrEmpty(serverUrl))
                {
                    MessageBox.Show("请输入真实后端服务器地址", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                if (!serverUrl.StartsWith("ws://") && !serverUrl.StartsWith("wss://"))
                {
                    MessageBox.Show("地址格式错误，请使用 ws:// 或 wss:// 开头", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 获取选择的场景
                string selectedScene = GetSelectedScene();
                Console.WriteLine($"[DEBUG] 选择的场景: {selectedScene}");

                // 连接前清空数据
                ClearAllData();

                StatusText.Text = "正在连接真实后端...";
                StatusText.Foreground = new SolidColorBrush(Colors.Orange);
                Console.WriteLine("[DEBUG] 开始连接过程...");

                _client = new CargoWebSocketClient(serverUrl);
                _client.DataReceived += OnDataReceived;
                _client.ConnectionStatusChanged += OnConnectionStatusChanged;
                
                Console.WriteLine("[DEBUG] 调用ConnectAsync...");
                await _client.ConnectAsync();
                
                _connectTime = DateTime.Now;
                ConnectTimeText.Text = _connectTime.ToString("HH:mm:ss");
                
                StatusText.Text = $"已连接 - 场景: {selectedScene}";
                StatusText.Foreground = new SolidColorBrush(Colors.Green);
                Console.WriteLine("[DEBUG] 连接成功完成");

                // 连接成功后发送ClientRequest - 使用选择的场景
                Console.WriteLine("[DEBUG] 连接成功，发送ClientRequest...");
                await _client.SendClientRequest("admin", selectedScene);
                Console.WriteLine($"[DEBUG] ClientRequest已发送，场景: {selectedScene}");
                
            } catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 连接按钮点击异常: {ex.Message}");
                Console.WriteLine($"[ERROR] 异常详情: {ex}");
                StatusText.Text = "连接失败";
                StatusText.Foreground = new SolidColorBrush(Colors.Red);
                MessageBox.Show($"连接真实后端失败: {ex.Message}", "连接错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DisconnectButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_client != null)
                {
                    Console.WriteLine("[DEBUG] 开始断开连接...");
                    
                    // 先更新UI状态，防止界面卡死
                    StatusText.Text = "正在断开连接...";
                    StatusText.Foreground = new SolidColorBrush(Colors.Orange);
                    DisconnectButton.IsEnabled = false;
                    
                    // 断开连接
                    await _client.DisconnectAsync();
                    _client = null;
                    
                    Console.WriteLine("[DEBUG] 连接已断开，客户端已清理");
                    
                    // 手动更新连接状态，确保UI正确更新
                    UpdateConnectionStatus(false);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 断开连接时发生异常: {ex.Message}");
                
                // 即使出现异常，也要确保UI状态正确
                _client = null;
                UpdateConnectionStatus(false);
                
                MessageBox.Show($"断开连接时发生错误: {ex.Message}", "断开连接错误", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ClearAllData();
        }

        private void OnDataReceived(object? sender, DataReceivedEventArgs e)
        {
            Console.WriteLine($"[DEBUG] OnDataReceived触发 - 数据类型: {e.DataType}, 设备ID: {e.DeviceId}");
            Console.WriteLine($"[DEBUG] 详情: {e.Details}");
            Console.WriteLine($"[DEBUG] 原始消息: {e.RawMessage}");
            
            Dispatcher.Invoke(() =>
            {
                Console.WriteLine("[DEBUG] 在UI线程中处理数据...");
                
                var dataItem = new DataItem
                {
                    Timestamp = DateTime.Now.ToString("HH:mm:ss.fff"),
                    DataType = e.DataType,
                    DeviceId = e.DeviceId,
                    Details = e.Details
                };

                _dataItems.Insert(0, dataItem);
                Console.WriteLine($"[DEBUG] 数据项已添加到列表，当前总数: {_dataItems.Count}");

                while (_dataItems.Count > 1000)
                {
                    _dataItems.RemoveAt(_dataItems.Count - 1);
                }

                _totalMessages++;
                TotalCountText.Text = _totalMessages.ToString();
                MessageSizeText.Text = $"{e.MessageSize} bytes";
                Console.WriteLine($"[DEBUG] 统计更新 - 总消息数: {_totalMessages}");

                var messagePrefix = "";
                if (e.DataType.StartsWith("Direct_"))
                {
                    messagePrefix = "[直接解析] ";
                }
                else if (e.DataType == "Error")
                {
                    messagePrefix = "[错误] ";
                }
                else if (e.DataType.Contains("MESSAGE"))
                {
                    messagePrefix = "[Message格式] ";
                }

                RawMessageTextBox.AppendText($"{messagePrefix}[{dataItem.Timestamp}] {e.RawMessage}\n");
                RawMessageTextBox.ScrollToEnd();
                Console.WriteLine("[DEBUG] UI更新完成");
            });
        }

        private void OnConnectionStatusChanged(object? sender, bool isConnected)
        {
            Console.WriteLine($"[DEBUG] 连接状态变化: {isConnected}");
            Dispatcher.Invoke(() => UpdateConnectionStatus(isConnected));
        }

        private void UpdateConnectionStatus(bool isConnected)
        {
            if (isConnected)
            {
                StatusText.Text = "已连接";
                StatusText.Foreground = Brushes.Green;
                ConnectButton.IsEnabled = false;
                DisconnectButton.IsEnabled = true;
                ServerUrlTextBox.IsEnabled = false; // 连接时禁用地址输入
                SceneComboBox.IsEnabled = false;    // 连接时禁用场景选择
            }
            else
            {
                StatusText.Text = "未连接";
                StatusText.Foreground = Brushes.Red;
                ConnectButton.IsEnabled = true;
                DisconnectButton.IsEnabled = false;
                ServerUrlTextBox.IsEnabled = true;  // 断开时启用地址输入
                SceneComboBox.IsEnabled = true;     // 断开时启用场景选择
            }
        }

        private async void SendHeartbeatButton_Click(object sender, RoutedEventArgs e)
        {
            if (_client != null)
            {
                Console.WriteLine("[DEBUG] 手动发送心跳...");
                await _client.SendHeartbeat();
            }
            else
            {
                Console.WriteLine("[ERROR] 客户端未连接");
            }
        }

        private async void SendSubscribeButton_Click(object sender, RoutedEventArgs e)
        {
            if (_client != null)
            {
                Console.WriteLine("[DEBUG] 发送ClientRequest认证...");
                string selectedScene = GetSelectedScene();
                await _client.SendClientRequest("admin", selectedScene);
                Console.WriteLine($"[DEBUG] ClientRequest认证已发送，场景: {selectedScene}");
            }
            else
            {
                Console.WriteLine("[ERROR] 客户端未连接");
                MessageBox.Show("请先连接到服务器", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void SendClientRequestButton_Click(object sender, RoutedEventArgs e)
        {
            if (_client != null)
            {
                Console.WriteLine("[DEBUG] 发送ClientRequest...");
                string userId = "user123";
                string selectedScene = GetSelectedScene();
                
                await _client.SendClientRequest(userId, selectedScene);
                Console.WriteLine($"[DEBUG] ClientRequest已发送，场景: {selectedScene}");
            }
            else
            {
                Console.WriteLine("[ERROR] 客户端未连接");
                MessageBox.Show("请先连接到服务器", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // 获取选择的场景
        private string GetSelectedScene()
        {
            if (SceneComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                return selectedItem.Content.ToString() ?? "scene1";
            }
            return "scene1"; // 默认场景
        }

        private void SceneComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 如果当前已连接，提示用户需要重新连接
            if (_client != null)
            {
                var result = MessageBox.Show(
                    "切换场景需要重新连接，是否断开当前连接？", 
                    "场景切换", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);
                    
                if (result == MessageBoxResult.Yes)
                {
                    // 异步断开连接
                    _ = Task.Run(async () =>
                    {
                        await Dispatcher.InvokeAsync(async () =>
                        {
                            await DisconnectAndClearData();
                        });
                    });
                }
                else
                {
                    // 用户取消，恢复之前的选择
                    // 这里可以记录之前的选择并恢复
                }
            }
            else
            {
                // 未连接状态下切换场景，只清空数据
                ClearAllData();
                Console.WriteLine($"[DEBUG] 场景已切换到: {GetSelectedScene()}");
            }
        }

        private async Task DisconnectAndClearData()
        {
            try
            {
                if (_client != null)
                {
                    Console.WriteLine("[DEBUG] 因场景切换断开连接...");
                    await _client.DisconnectAsync();
                    _client = null;
                }
                
                ClearAllData();
                UpdateConnectionStatus(false);
                
                StatusText.Text = $"已切换到场景: {GetSelectedScene()}";
                StatusText.Foreground = new SolidColorBrush(Colors.Blue);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 场景切换断开连接异常: {ex.Message}");
                ClearAllData();
                UpdateConnectionStatus(false);
            }
        }

        private void ClearAllData()
        {
            try
            {
                Console.WriteLine("[DEBUG] 清空所有数据显示...");
                
                // 清空数据列表
                _dataItems.Clear();
                
                // 清空原始消息显示
                RawMessageTextBox.Clear();
                
                // 重置统计信息
                _totalMessages = 0;
                TotalCountText.Text = "0";
                MessageSizeText.Text = "0 bytes";
                
                // 重置连接时间显示
                ConnectTimeText.Text = "--:--:--";
                
                Console.WriteLine("[DEBUG] 数据清空完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 清空数据时异常: {ex.Message}");
            }
        }

        private void DataListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DataListView.SelectedItem is DataItem selectedItem)
            {
                _selectedDataItem = selectedItem;
                Console.WriteLine($"[DEBUG] 选中数据项: {selectedItem.DataType} - {selectedItem.DeviceId}");
            }
            else
            {
                _selectedDataItem = null;
            }
        }

        private void MainWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // 检查是否按下 Ctrl + Shift + 1
            if (e.Key == System.Windows.Input.Key.D1 && 
                System.Windows.Input.Keyboard.Modifiers == (System.Windows.Input.ModifierKeys.Control | System.Windows.Input.ModifierKeys.Shift))
            {
                if (_selectedDataItem != null)
                {
                    ShowDataDetailModal(_selectedDataItem);
                }
                else
                {
                    MessageBox.Show("请先选择一条数据记录", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                e.Handled = true;
            }
            // ESC键关闭弹框
            else if (e.Key == System.Windows.Input.Key.Escape && _isDetailModalOpen)
            {
                CloseDataDetailModal();
                e.Handled = true;
            }
        }

        private void ShowDataDetailModal(DataItem dataItem)
        {
            try
            {
                _isDetailModalOpen = true;
                
                // 设置弹框内容
                DetailModalTitle.Text = $"数据详情 - {dataItem.DataType}";
                
                var detailInfo = FormatDataItemDetails(dataItem);
                
                DetailModalContent.Text = detailInfo;
                
                // 显示弹框
                DetailModalOverlay.Visibility = Visibility.Visible;
                
                Console.WriteLine($"[DEBUG] 显示数据详情: {dataItem.DataType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 显示数据详情时异常: {ex.Message}");
                _isDetailModalOpen = false;
            }
        }

        private string FormatDataItemDetails(DataItem dataItem)
        {
            var sb = new System.Text.StringBuilder();
            
            // 基本信息
            sb.AppendLine("═══════════════════════════════════════");
            sb.AppendLine("                基本信息");
            sb.AppendLine("═══════════════════════════════════════");
            sb.AppendLine($"时间戳        : {dataItem.Timestamp}");
            sb.AppendLine($"数据类型      : {dataItem.DataType}");
            sb.AppendLine($"设备ID        : {dataItem.DeviceId}");
            sb.AppendLine();
            
            // 详细信息解析
            sb.AppendLine("═══════════════════════════════════════");
            sb.AppendLine("                详细数据");
            sb.AppendLine("═══════════════════════════════════════");
            
            if (!string.IsNullOrEmpty(dataItem.Details))
            {
                // 尝试解析Details字段中的键值对
                var details = ParseDetailsString(dataItem.Details);
                
                if (details.Count > 0)
                {
                    foreach (var kvp in details)
                    {
                        var value = FormatFieldValue(kvp.Value);
                        sb.AppendLine($"{kvp.Key,-20} : {value}");
                    }
                }
                else
                {
                    // 如果无法解析为键值对，直接显示原始内容
                    sb.AppendLine(dataItem.Details);
                }
            }
            else
            {
                sb.AppendLine("无详细数据");
            }
            
            sb.AppendLine();
            sb.AppendLine("═══════════════════════════════════════");
            sb.AppendLine($"查看时间      : {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine("═══════════════════════════════════════");
            
            return sb.ToString();
        }

        private Dictionary<string, string> ParseDetailsString(string details)
        {
            var result = new Dictionary<string, string>();
            
            try
            {
                // 尝试解析逗号分隔的键值对格式: key1=value1, key2=value2
                if (details.Contains("="))
                {
                    var pairs = details.Split(new[] { ", " }, StringSplitOptions.RemoveEmptyEntries);
                    
                    foreach (var pair in pairs)
                    {
                        var keyValue = pair.Split(new[] { '=' }, 2);
                        if (keyValue.Length == 2)
                        {
                            var key = keyValue[0].Trim();
                            var value = keyValue[1].Trim();
                            result[key] = value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] 解析Details字符串失败: {ex.Message}");
            }
            
            return result;
        }

        private string FormatFieldValue(string value)
        {
            // 格式化字段值显示
            if (string.IsNullOrEmpty(value))
                return "无数据";
            
            // 如果是数字，尝试格式化
            if (double.TryParse(value, out double numValue))
            {
                // 如果是整数，不显示小数点
                if (numValue % 1 == 0)
                    return numValue.ToString("F0");
                else
                    return numValue.ToString("F2");
            }
            
            // 如果是布尔值
            if (bool.TryParse(value, out bool boolValue))
            {
                return boolValue ? "是" : "否";
            }
            
            return value;
        }

        private void CloseDataDetailModal()
        {
            DetailModalOverlay.Visibility = Visibility.Collapsed;
            _isDetailModalOpen = false;
            Console.WriteLine("[DEBUG] 关闭数据详情弹框");
        }

        private void CloseDetailModal_Click(object sender, RoutedEventArgs e)
        {
            CloseDataDetailModal();
        }
    }

    public class DataReceivedEventArgs : EventArgs
    {
        public string DataType { get; set; } = "";
        public string DeviceId { get; set; } = "";
        public string Details { get; set; } = "";
        public string RawMessage { get; set; } = "";
        public int MessageSize { get; set; }
    }
}




































