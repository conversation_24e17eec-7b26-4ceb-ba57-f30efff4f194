syntax = "proto3";

package cargo;

// 通用数据字段类型
enum FieldType {
  STRING = 0;
  DOUBLE = 1;
  INT32 = 2;
  INT64 = 3;
  BOOL = 4;
  BYTES = 5;
}

// 通用数据字段
message DataField {
  string key = 1;           // 字段名称
  FieldType type = 2;       // 字段类型
  string stringValue = 3;   // 字符串值
  double doubleValue = 4;   // 浮点数值
  int64 intValue = 5;       // 整数值
  bool boolValue = 6;       // 布尔值
  bytes bytesValue = 7;     // 字节值
  repeated DataField arrayValue = 8; // 数组值（用于嵌套结构）
}

// 通用状态数据
message CommonStatus {
  string id = 1;                        // 设备/实体ID
  string type = 2;                      // 数据类型标识 (dev_loc, dcv_base, csp_time等)
  repeated DataField fields = 3;        // 动态字段列表
  int64 timestamp = 4;                  // 时间戳
  map<string, string> metadata = 5;     // 元数据
}

// 通用数据批次
message CommonData {
  string insertDate = 1;                // 插入日期
  string dataType = 2;                  // 数据类型 (dev_loc, dcv_base, csp_time等)
  repeated CommonStatus statusList = 3;  // 状态列表
  map<string, string> config = 4;       // 配置信息
}

// 通用数据批次集合
message CommonDataBatch {
  repeated CommonData dataItems = 1;
  int32 totalCount = 2;
  string batchId = 3;
  int64 batchTimestamp = 4;
}