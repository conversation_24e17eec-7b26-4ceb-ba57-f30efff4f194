syntax = "proto3";

package cargo;

option java_package = "com.example.nettydemo.protobuf";
option java_outer_classname = "MessageProto";

enum MessageType {
  UNKNOWN = 0;
  CARGO_DATA = 1;
  CARGO_BATCH = 2;
  COMMON_DATA = 3;
  MESSAGE = 4;
  HEARTBEAT = 5;
  SUBSCRIBE = 6;
}

message WebSocketMessage {
  MessageType type = 1;
  bytes payload = 2;
  int64 timestamp = 3;
  string messageId = 4;
}

message DataField {
  string key = 1;
  int32 type = 2;
  string stringValue = 3;
  double doubleValue = 4;
  int32 intValue = 5;
}

message CommonStatus {
  string id = 1;
  repeated DataField fields = 2;
}

message CommonData {
  string dataType = 1;
  string insertDate = 2;
  repeated CommonStatus statusList = 3;
}

message CargoData {
  string id = 1;
  string name = 2;
  string status = 3;
  double weight = 4;
  string location = 5;
  int64 timestamp = 6;
}

message CargoBatch {
  repeated CargoData items = 1;
  int32 totalCount = 2;
  int32 batchSize = 3;
}




