# WebSocket 加密传输实现示例

## 概述

本示例展示如何在现有的 Java(Kafka) → Node.js(WebSocket) → C# 架构中添加端到端加密。

## 架构图

```
[Kafka] → [Java Backend] → [Node.js WebSocket Server] → [C# Client]
                              ↓ (加密层)
                         [AES-256-GCM 加密]
                         [RSA 密钥交换]
```

## 实现方案

### 1. 传输层加密 (WSS) - 推荐首选

最简单且安全的方案，只需配置SSL证书：

```javascript
// Node.js 服务器
const https = require('https');
const fs = require('fs');
const { WebSocketServer } = require('ws');

const server = https.createServer({
  cert: fs.readFileSync('/path/to/cert.pem'),
  key: fs.readFileSync('/path/to/key.pem')
});

const wss = new WebSocketServer({ server });
server.listen(8443);
```

```csharp
// C# 客户端
var client = new CargoWebSocketClient("wss://your-server:8443");
await client.ConnectAsync();
```

### 2. 应用层加密 (AES-GCM)

适用于需要端到端加密的场景：

#### 启动服务器（启用加密）

```bash
# 设置环境变量启用加密
export ENCRYPTION_ENABLED=true
node src/server/index.js
```

#### C# 客户端使用加密

```csharp
// 启用加密的客户端
var encryptionKey = EncryptionUtils.GenerateRandomKey();
var client = new CargoWebSocketClient("ws://localhost:8080", true, encryptionKey);

await client.ConnectAsync();
```

## 密钥管理最佳实践

### 1. 开发环境

```javascript
// 使用固定密钥（仅开发环境）
const fixedKey = Buffer.from('your-32-byte-key-here-for-development', 'utf8');
const encryptionUtils = new EncryptionUtils(fixedKey);
```

### 2. 生产环境

```javascript
// 使用环境变量
const keyBase64 = process.env.ENCRYPTION_KEY;
const encryptionKey = Buffer.from(keyBase64, 'base64');
const encryptionUtils = new EncryptionUtils(encryptionKey);
```

### 3. 密钥轮换

```javascript
// 定期轮换密钥
setInterval(() => {
  keyManager.rotateKeys();
}, 24 * 60 * 60 * 1000); // 每24小时轮换
```

## 性能考虑

### AES-GCM 性能特点

- **加密速度**: ~1GB/s (现代CPU)
- **内存开销**: 每消息 +28字节 (IV + AuthTag)
- **CPU开销**: ~2-5% (高频消息场景)

### 优化建议

1. **批量加密**: 将多个小消息合并后加密
2. **缓存密钥**: 避免重复创建加密器实例
3. **异步处理**: 在独立线程中进行加密/解密

## 安全最佳实践

### 1. 密钥管理

```javascript
// ✅ 正确：使用环境变量
const key = process.env.ENCRYPTION_KEY;

// ❌ 错误：硬编码密钥
const key = "hardcoded-key-123";
```

### 2. 密钥存储

```csharp
// ✅ 正确：使用安全存储
var keyManager = new KeyManager();
await keyManager.LoadAESKey();

// ❌ 错误：明文存储
File.WriteAllText("key.txt", Convert.ToBase64String(key));
```

### 3. 错误处理

```javascript
// ✅ 正确：安全的错误处理
try {
  const decrypted = encryptionUtils.decrypt(data);
} catch (error) {
  console.log('解密失败'); // 不泄露具体错误信息
  return;
}
```

## 部署配置

### Docker 环境

```dockerfile
# Dockerfile
ENV ENCRYPTION_ENABLED=true
ENV ENCRYPTION_KEY=your-base64-encoded-key

# 或使用 Docker Secrets
COPY encryption_key.txt /run/secrets/encryption_key
```

### Kubernetes 环境

```yaml
# k8s-secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: websocket-encryption
data:
  encryption-key: <base64-encoded-key>
```

## 监控和日志

### 加密状态监控

```javascript
// 监控加密状态
app.get('/health', (req, res) => {
  res.json({
    encryption: {
      enabled: encryptionEnabled,
      activeClients: clients.size,
      encryptedMessages: encryptionStats.totalMessages
    }
  });
});
```

### 安全日志

```javascript
// 记录安全事件（不记录敏感信息）
console.log(`[SECURITY] 客户端 ${clientId} 加密连接建立`);
console.log(`[SECURITY] 检测到解密失败，可能的攻击尝试`);
```

## 故障排除

### 常见问题

1. **解密失败**
   - 检查密钥是否一致
   - 验证数据完整性
   - 确认加密算法参数

2. **性能问题**
   - 监控CPU使用率
   - 检查内存泄漏
   - 优化消息大小

3. **连接问题**
   - 验证SSL证书
   - 检查防火墙设置
   - 确认端口配置

## 测试

### 单元测试

```javascript
// 测试加密/解密
const originalData = Buffer.from('test message');
const encrypted = encryptionUtils.encrypt(originalData);
const decrypted = encryptionUtils.decrypt(encrypted);
assert(originalData.equals(decrypted));
```

### 集成测试

```csharp
// 测试端到端加密
var client = new CargoWebSocketClient("ws://localhost:8080", true);
await client.ConnectAsync();
await client.SendClientRequest("testUser", "testScene");
// 验证服务器收到并正确解密消息
```

## 迁移指南

### 从明文到加密的平滑迁移

1. **阶段1**: 部署支持加密的服务器（向后兼容）
2. **阶段2**: 逐步迁移客户端到加密模式
3. **阶段3**: 禁用明文模式

```javascript
// 向后兼容的服务器实现
const isEncrypted = detectEncryption(messageData);
if (isEncrypted) {
  messageData = encryptionUtils.decrypt(messageData);
}
```
