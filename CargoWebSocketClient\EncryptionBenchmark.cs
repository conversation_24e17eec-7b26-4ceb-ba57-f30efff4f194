using System;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace CargoWebSocketClient
{
    /// <summary>
    /// C# 加密性能基准测试
    /// </summary>
    public class EncryptionBenchmark
    {
        private readonly EncryptionUtils _encryptionUtils;

        public EncryptionBenchmark()
        {
            _encryptionUtils = new EncryptionUtils();
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        private byte[] GenerateTestData(int size)
        {
            var data = new byte[size];
            RandomNumberGenerator.Fill(data);
            return data;
        }

        /// <summary>
        /// AES-GCM 性能测试
        /// </summary>
        public async Task<BenchmarkResult> BenchmarkAESGCM(int dataSize, int iterations = 1000)
        {
            Console.WriteLine($"\n=== AES-256-GCM 性能测试 ===");
            Console.WriteLine($"数据大小: {dataSize} bytes, 迭代次数: {iterations}");

            var testData = GenerateTestData(dataSize);
            var encryptedResults = new byte[iterations][];

            // 加密性能测试
            var encryptStopwatch = Stopwatch.StartNew();
            
            for (int i = 0; i < iterations; i++)
            {
                encryptedResults[i] = _encryptionUtils.Encrypt(testData);
            }
            
            encryptStopwatch.Stop();

            // 解密性能测试
            var decryptStopwatch = Stopwatch.StartNew();
            
            for (int i = 0; i < iterations; i++)
            {
                _encryptionUtils.Decrypt(encryptedResults[i]);
            }
            
            decryptStopwatch.Stop();

            // 计算性能指标
            var totalDataMB = (double)(dataSize * iterations) / (1024 * 1024);
            var encryptThroughput = totalDataMB / (encryptStopwatch.ElapsedMilliseconds / 1000.0);
            var decryptThroughput = totalDataMB / (decryptStopwatch.ElapsedMilliseconds / 1000.0);

            var result = new BenchmarkResult
            {
                DataSize = dataSize,
                Iterations = iterations,
                EncryptTime = encryptStopwatch.ElapsedMilliseconds,
                DecryptTime = decryptStopwatch.ElapsedMilliseconds,
                EncryptThroughput = encryptThroughput,
                DecryptThroughput = decryptThroughput,
                AvgEncryptLatency = (double)encryptStopwatch.ElapsedMilliseconds / iterations,
                AvgDecryptLatency = (double)decryptStopwatch.ElapsedMilliseconds / iterations,
                Overhead = ((double)(encryptedResults[0].Length - dataSize) / dataSize) * 100
            };

            Console.WriteLine($"加密时间: {result.EncryptTime}ms");
            Console.WriteLine($"解密时间: {result.DecryptTime}ms");
            Console.WriteLine($"加密吞吐量: {result.EncryptThroughput:F2} MB/s");
            Console.WriteLine($"解密吞吐量: {result.DecryptThroughput:F2} MB/s");
            Console.WriteLine($"平均加密延迟: {result.AvgEncryptLatency:F3}ms");
            Console.WriteLine($"平均解密延迟: {result.AvgDecryptLatency:F3}ms");
            Console.WriteLine($"数据膨胀率: {result.Overhead:F1}%");

            return result;
        }

        /// <summary>
        /// 测试不同数据大小的性能
        /// </summary>
        public async Task BenchmarkDifferentSizes()
        {
            Console.WriteLine($"\n=== 不同数据大小性能测试 ===");

            var testCases = new[]
            {
                new { Name = "小消息 (64B)", Size = 64, Iterations = 50000 },
                new { Name = "中等消息 (1KB)", Size = 1024, Iterations = 10000 },
                new { Name = "大消息 (10KB)", Size = 10240, Iterations = 1000 },
                new { Name = "超大消息 (100KB)", Size = 102400, Iterations = 100 },
                new { Name = "巨型消息 (1MB)", Size = 1048576, Iterations = 10 }
            };

            foreach (var testCase in testCases)
            {
                Console.WriteLine($"\n--- {testCase.Name} ---");
                await BenchmarkAESGCM(testCase.Size, testCase.Iterations);
            }
        }

        /// <summary>
        /// 内存使用测试
        /// </summary>
        public async Task BenchmarkMemoryUsage()
        {
            Console.WriteLine($"\n=== 内存使用测试 ===");

            var initialMemory = GC.GetTotalMemory(false);
            Console.WriteLine($"初始内存使用: {initialMemory / 1024.0 / 1024.0:F2} MB");

            // 创建大量加密数据
            var testData = GenerateTestData(1024);
            var encryptedData = new byte[10000][];

            for (int i = 0; i < 10000; i++)
            {
                encryptedData[i] = _encryptionUtils.Encrypt(testData);
            }

            var afterEncryption = GC.GetTotalMemory(false);
            Console.WriteLine($"加密后内存使用: {afterEncryption / 1024.0 / 1024.0:F2} MB");
            Console.WriteLine($"内存增长: {(afterEncryption - initialMemory) / 1024.0 / 1024.0:F2} MB");

            // 解密所有数据
            for (int i = 0; i < encryptedData.Length; i++)
            {
                _encryptionUtils.Decrypt(encryptedData[i]);
            }

            var afterDecryption = GC.GetTotalMemory(false);
            Console.WriteLine($"解密后内存使用: {afterDecryption / 1024.0 / 1024.0:F2} MB");

            // 强制垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var afterGC = GC.GetTotalMemory(true);
            Console.WriteLine($"GC后内存使用: {afterGC / 1024.0 / 1024.0:F2} MB");
        }

        /// <summary>
        /// 并发性能测试
        /// </summary>
        public async Task BenchmarkConcurrency()
        {
            Console.WriteLine($"\n=== 并发性能测试 ===");

            var testData = GenerateTestData(1024);
            var concurrencyLevels = new[] { 1, 2, 4, 8, 16 };

            foreach (var concurrency in concurrencyLevels)
            {
                var stopwatch = Stopwatch.StartNew();
                var tasks = new Task[concurrency];

                for (int i = 0; i < concurrency; i++)
                {
                    tasks[i] = RunConcurrentTest(testData, 1000);
                }

                await Task.WhenAll(tasks);
                stopwatch.Stop();

                var totalOps = concurrency * 1000 * 2; // 加密+解密
                var opsPerSecond = totalOps / (stopwatch.ElapsedMilliseconds / 1000.0);

                Console.WriteLine($"并发度 {concurrency}: {stopwatch.ElapsedMilliseconds}ms, {opsPerSecond:F0} ops/s");
            }
        }

        private async Task RunConcurrentTest(byte[] testData, int iterations)
        {
            await Task.Run(() =>
            {
                for (int i = 0; i < iterations; i++)
                {
                    var encrypted = _encryptionUtils.Encrypt(testData);
                    _encryptionUtils.Decrypt(encrypted);
                }
            });
        }

        /// <summary>
        /// 生成性能报告
        /// </summary>
        public void GenerateReport()
        {
            Console.WriteLine($"\n=== 性能测试报告 ===");
            Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($".NET版本: {Environment.Version}");
            Console.WriteLine($"平台: {Environment.OSVersion}");
            Console.WriteLine($"处理器数量: {Environment.ProcessorCount}");
        }

        /// <summary>
        /// 运行所有基准测试
        /// </summary>
        public async Task RunAllBenchmarks()
        {
            try
            {
                await BenchmarkDifferentSizes();
                await BenchmarkMemoryUsage();
                await BenchmarkConcurrency();
                GenerateReport();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"基准测试失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 基准测试结果
    /// </summary>
    public class BenchmarkResult
    {
        public int DataSize { get; set; }
        public int Iterations { get; set; }
        public long EncryptTime { get; set; }
        public long DecryptTime { get; set; }
        public double EncryptThroughput { get; set; }
        public double DecryptThroughput { get; set; }
        public double AvgEncryptLatency { get; set; }
        public double AvgDecryptLatency { get; set; }
        public double Overhead { get; set; }
    }
}
