{"name": "websocket-protobuf-demo", "version": "1.0.0", "type": "module", "scripts": {"proto": "protoc --js_out=import_style=commonjs,binary:./src/proto --proto_path=./proto ./proto/*.proto", "proto:individual": "protoc --js_out=import_style=commonjs,binary:./src/proto --proto_path=./proto ./proto/cargo.proto ./proto/csp_time.proto ./proto/dev_loc.proto ./proto/dcv_base.proto", "server": "node src/server/index.js", "dev": "concurrently \"npm run server\" \"live-server --port=3000 src/client\""}, "dependencies": {"ws": "^8.14.2", "protobufjs": "^7.2.5", "google-protobuf": "^3.21.2"}, "devDependencies": {"concurrently": "^8.2.2", "live-server": "^1.2.2"}}