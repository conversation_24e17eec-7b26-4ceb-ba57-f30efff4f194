import crypto from 'crypto';

/**
 * AES-GCM 加密工具类
 * 适用于高性能实时数据传输加密
 */
class EncryptionUtils {
    constructor(secretKey) {
        // 32字节密钥用于AES-256
        this.secretKey = secretKey || crypto.randomBytes(32);
        this.algorithm = 'aes-256-gcm';
    }

    /**
     * 加密Protocol Buffer数据
     * @param {Buffer} data - 原始protobuf数据
     * @returns {Buffer} - 加密后的数据 (IV + AuthTag + EncryptedData)
     */
    encrypt(data) {
        try {
            // 生成随机IV (12字节用于GCM)
            const iv = crypto.randomBytes(12);
            
            // 创建加密器
            const cipher = crypto.createCipheriv(this.algorithm, this.secretKey, iv);
            cipher.setAAD(Buffer.from('websocket-protobuf')); // 附加认证数据

            // 加密数据
            const encrypted = Buffer.concat([
                cipher.update(data),
                cipher.final()
            ]);

            // 获取认证标签
            const authTag = cipher.getAuthTag();
            
            // 组合: IV(12) + AuthTag(16) + EncryptedData
            return Buffer.concat([iv, authTag, encrypted]);
            
        } catch (error) {
            console.error('加密失败:', error);
            throw new Error('数据加密失败');
        }
    }

    /**
     * 解密数据
     * @param {Buffer} encryptedData - 加密的数据
     * @returns {Buffer} - 解密后的protobuf数据
     */
    decrypt(encryptedData) {
        try {
            if (encryptedData.length < 28) { // 12(IV) + 16(AuthTag) = 28
                throw new Error('加密数据格式错误');
            }
            
            // 提取组件
            const iv = encryptedData.slice(0, 12);
            const authTag = encryptedData.slice(12, 28);
            const encrypted = encryptedData.slice(28);
            
            // 创建解密器
            const decipher = crypto.createDecipheriv(this.algorithm, this.secretKey, iv);
            decipher.setAuthTag(authTag);
            decipher.setAAD(Buffer.from('websocket-protobuf'));
            
            // 解密数据
            const decrypted = Buffer.concat([
                decipher.update(encrypted),
                decipher.final()
            ]);
            
            return decrypted;
            
        } catch (error) {
            console.error('解密失败:', error);
            throw new Error('数据解密失败');
        }
    }

    /**
     * 生成密钥交换用的密钥对
     */
    static generateKeyPair() {
        return crypto.generateKeyPairSync('rsa', {
            modulusLength: 2048,
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem'
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem'
            }
        });
    }

    /**
     * 使用RSA加密AES密钥 (用于密钥交换)
     */
    static encryptKey(aesKey, publicKey) {
        return crypto.publicEncrypt(publicKey, aesKey);
    }

    /**
     * 使用RSA解密AES密钥
     */
    static decryptKey(encryptedKey, privateKey) {
        return crypto.privateDecrypt(privateKey, encryptedKey);
    }
}

export default EncryptionUtils;
