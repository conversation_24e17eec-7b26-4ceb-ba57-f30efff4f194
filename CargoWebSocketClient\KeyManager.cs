using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace CargoWebSocketClient
{
    /// <summary>
    /// 客户端密钥管理器
    /// </summary>
    public class KeyManager
    {
        private readonly string _keyStoragePath;
        private byte[]? _aesKey;
        private string? _serverPublicKey;

        public KeyManager(string? keyStoragePath = null)
        {
            _keyStoragePath = keyStoragePath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CargoWebSocketClient",
                "keys"
            );
            
            Directory.CreateDirectory(_keyStoragePath);
        }

        /// <summary>
        /// 密钥交换响应数据结构
        /// </summary>
        public class KeyExchangeResponse
        {
            public string EncryptedKey { get; set; } = string.Empty;
            public string ServerPublicKey { get; set; } = string.Empty;
            public string KeyId { get; set; } = string.Empty;
        }

        /// <summary>
        /// 处理服务器的密钥交换响应
        /// </summary>
        public async Task<byte[]> ProcessKeyExchange(KeyExchangeResponse response)
        {
            try
            {
                Console.WriteLine("[INFO] 开始处理密钥交换...");
                
                // 保存服务器公钥
                _serverPublicKey = response.ServerPublicKey;
                await SaveServerPublicKey(response.ServerPublicKey);
                
                // 解密AES密钥
                var encryptedKeyBytes = Convert.FromBase64String(response.EncryptedKey);
                
                // 这里应该使用客户端的私钥解密，但为了简化示例，我们假设使用预共享密钥
                // 在实际应用中，客户端应该有自己的RSA密钥对
                _aesKey = DecryptAESKey(encryptedKeyBytes, response.ServerPublicKey);
                
                // 保存密钥到本地
                await SaveAESKey(_aesKey, response.KeyId);
                
                Console.WriteLine("[INFO] 密钥交换完成");
                return _aesKey;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 密钥交换失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 解密AES密钥（简化版本）
        /// 实际应用中应该使用客户端私钥
        /// </summary>
        private byte[] DecryptAESKey(byte[] encryptedKey, string serverPublicKey)
        {
            // 注意：这是一个简化的实现
            // 在实际应用中，客户端应该：
            // 1. 拥有自己的RSA密钥对
            // 2. 将公钥发送给服务器
            // 3. 服务器使用客户端公钥加密AES密钥
            // 4. 客户端使用自己的私钥解密
            
            // 为了演示，这里生成一个固定的密钥
            // 实际应用中绝对不要这样做！
            using (var sha256 = SHA256.Create())
            {
                var keyMaterial = Encoding.UTF8.GetBytes(serverPublicKey + "client_secret");
                return sha256.ComputeHash(keyMaterial);
            }
        }

        /// <summary>
        /// 保存AES密钥到本地文件
        /// </summary>
        private async Task SaveAESKey(byte[] aesKey, string keyId)
        {
            try
            {
                var keyData = new
                {
                    KeyId = keyId,
                    Key = Convert.ToBase64String(aesKey),
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddDays(30) // 30天过期
                };
                
                var json = JsonSerializer.Serialize(keyData, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                var keyFilePath = Path.Combine(_keyStoragePath, "aes_key.json");
                await File.WriteAllTextAsync(keyFilePath, json);
                
                Console.WriteLine($"[INFO] AES密钥已保存到: {keyFilePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 保存AES密钥失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存服务器公钥
        /// </summary>
        private async Task SaveServerPublicKey(string publicKey)
        {
            try
            {
                var publicKeyPath = Path.Combine(_keyStoragePath, "server_public.pem");
                await File.WriteAllTextAsync(publicKeyPath, publicKey);
                Console.WriteLine($"[INFO] 服务器公钥已保存到: {publicKeyPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 保存服务器公钥失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从本地文件加载AES密钥
        /// </summary>
        public async Task<byte[]?> LoadAESKey()
        {
            try
            {
                var keyFilePath = Path.Combine(_keyStoragePath, "aes_key.json");
                
                if (!File.Exists(keyFilePath))
                {
                    Console.WriteLine("[INFO] 本地未找到AES密钥文件");
                    return null;
                }
                
                var json = await File.ReadAllTextAsync(keyFilePath);
                var keyData = JsonSerializer.Deserialize<JsonElement>(json);
                
                // 检查密钥是否过期
                if (keyData.TryGetProperty("ExpiresAt", out var expiresAtElement))
                {
                    if (DateTime.TryParse(expiresAtElement.GetString(), out var expiresAt))
                    {
                        if (DateTime.UtcNow > expiresAt)
                        {
                            Console.WriteLine("[INFO] 本地AES密钥已过期");
                            return null;
                        }
                    }
                }
                
                if (keyData.TryGetProperty("Key", out var keyElement))
                {
                    _aesKey = Convert.FromBase64String(keyElement.GetString()!);
                    Console.WriteLine("[INFO] 已从本地加载AES密钥");
                    return _aesKey;
                }
                
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 加载AES密钥失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前AES密钥
        /// </summary>
        public byte[]? GetAESKey()
        {
            return _aesKey;
        }

        /// <summary>
        /// 清除所有密钥
        /// </summary>
        public async Task ClearKeys()
        {
            try
            {
                _aesKey = null;
                _serverPublicKey = null;
                
                var files = Directory.GetFiles(_keyStoragePath);
                foreach (var file in files)
                {
                    File.Delete(file);
                }
                
                Console.WriteLine("[INFO] 所有密钥已清除");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 清除密钥失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成客户端RSA密钥对（用于真实的密钥交换）
        /// </summary>
        public static (string publicKey, string privateKey) GenerateClientKeyPair()
        {
            using (var rsa = RSA.Create(2048))
            {
                var publicKey = rsa.ExportRSAPublicKeyPem();
                var privateKey = rsa.ExportRSAPrivateKeyPem();
                return (publicKey, privateKey);
            }
        }
    }
}
