using System;
using System.Collections.Generic;
using System.IO;
using System.Net.WebSockets;
using System.Threading;
using System.Threading.Tasks;
using Google.Protobuf;
using Cargo;
using System.Linq;

namespace CargoWebSocketClient
{
    public class CargoWebSocketClient
    {
        private readonly string _uri;
        private ClientWebSocket? _webSocket;
        private CancellationTokenSource? _cancellationTokenSource;
        private readonly bool _encryptionEnabled;
        private readonly EncryptionUtils? _encryptionUtils;

        public event EventHandler<DataReceivedEventArgs>? DataReceived;
        public event EventHandler<bool>? ConnectionStatusChanged;

        public CargoWebSocketClient(string uri, bool encryptionEnabled = false, byte[]? encryptionKey = null)
        {
            _uri = uri;
            _encryptionEnabled = encryptionEnabled;
            _encryptionUtils = encryptionEnabled ? new EncryptionUtils(encryptionKey) : null;

            Console.WriteLine($"[INFO] 加密模式: {(_encryptionEnabled ? "启用" : "禁用")}");
        }

        public async Task ConnectAsync()
        {
            Console.WriteLine($"[DEBUG] 开始连接到: {_uri}");
            
            _webSocket = new ClientWebSocket();
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                Console.WriteLine("[DEBUG] 正在建立WebSocket连接...");
                await _webSocket.ConnectAsync(new Uri(_uri), _cancellationTokenSource.Token);
                Console.WriteLine("[DEBUG] WebSocket连接建立成功");
                
                ConnectionStatusChanged?.Invoke(this, true);
                
                Console.WriteLine("[DEBUG] 启动接收循环...");
                _ = Task.Run(ReceiveLoop);
                Console.WriteLine("[DEBUG] 接收循环已启动");
                
                // 移除自动发送订阅消息，连接成功后不自动发送任何消息
                // 让MainWindow在连接成功后手动发送ClientRequest
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 连接失败: {ex.Message}");
                ConnectionStatusChanged?.Invoke(this, false);
                throw;
            }
        }

        private async Task ReceiveLoop()
        {
            var buffer = new byte[1024 * 16]; // 增加到16KB
            var messageBuffer = new List<byte>();
            
            try
            {
                while (_webSocket?.State == WebSocketState.Open && !(_cancellationTokenSource?.Token.IsCancellationRequested ?? true))
                {
                    Console.WriteLine("[DEBUG] 等待接收消息...");
                    
                    var result = await _webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), _cancellationTokenSource.Token);
                    
                    Console.WriteLine($"[DEBUG] 收到消息片段 - 类型: {result.MessageType}, 大小: {result.Count} bytes, 是否结束: {result.EndOfMessage}");
                    
                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        Console.WriteLine("[DEBUG] 收到关闭消息");
                        break;
                    }

                    if (result.MessageType == WebSocketMessageType.Binary)
                    {
                        // 将接收到的数据添加到消息缓冲区
                        for (int i = 0; i < result.Count; i++)
                        {
                            messageBuffer.Add(buffer[i]);
                        }
                        
                        // 如果消息完整（EndOfMessage为true），处理完整消息
                        if (result.EndOfMessage)
                        {
                            Console.WriteLine($"[DEBUG] 收到完整二进制消息，总大小: {messageBuffer.Count} bytes");
                            
                            // 显示前20字节
                            var displayCount = Math.Min(20, messageBuffer.Count);
                            var hexBytes = new string[displayCount];
                            for (int i = 0; i < displayCount; i++)
                            {
                                hexBytes[i] = messageBuffer[i].ToString("X2");
                            }
                            Console.WriteLine($"[DEBUG] 前20字节: {string.Join(" ", hexBytes)}");
                            
                            var completeMessage = messageBuffer.ToArray();
                            
                            Console.WriteLine("[DEBUG] 开始处理完整消息...");
                            ProcessMessage(completeMessage);
                            Console.WriteLine("[DEBUG] 消息处理完成");
                            
                            // 清空缓冲区准备接收下一条消息
                            messageBuffer.Clear();
                        }
                        else
                        {
                            Console.WriteLine($"[DEBUG] 消息未完整，当前缓冲区大小: {messageBuffer.Count} bytes，继续接收...");
                        }
                    }
                    else if (result.MessageType == WebSocketMessageType.Text)
                    {
                        // 文本消息也可能分片
                        for (int i = 0; i < result.Count; i++)
                        {
                            messageBuffer.Add(buffer[i]);
                        }
                        
                        if (result.EndOfMessage)
                        {
                            Console.WriteLine($"[DEBUG] 收到完整文本消息，大小: {messageBuffer.Count} bytes");
                            var textData = System.Text.Encoding.UTF8.GetString(messageBuffer.ToArray());
                            Console.WriteLine($"[DEBUG] 文本内容: {textData}");
                            messageBuffer.Clear();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] ReceiveLoop异常: {ex.Message}");
                Console.WriteLine($"[ERROR] 异常详情: {ex}");
                ConnectionStatusChanged?.Invoke(this, false);
            }
            
            Console.WriteLine("[DEBUG] ReceiveLoop结束");
        }

        private void ProcessMessage(byte[] messageData)
        {
            Console.WriteLine($"[DEBUG] ProcessMessage开始 - 数据大小: {messageData.Length} bytes");

            if (messageData.Length == 0)
            {
                Console.WriteLine("[ERROR] 接收到空消息");
                return;
            }

            // 如果启用了加密，先解密数据
            if (_encryptionEnabled && _encryptionUtils != null)
            {
                try
                {
                    messageData = _encryptionUtils.Decrypt(messageData);
                    Console.WriteLine($"[DEBUG] 消息已解密，解密后大小: {messageData.Length} bytes");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[ERROR] 解密失败: {ex.Message}");
                    return;
                }
            }
            

            // 如果数据较小，显示完整的十六进制数据
            if (messageData.Length <= 200)
            {
                var allHexBytes = messageData.Select(b => b.ToString("X2"));
                Console.WriteLine($"[DEBUG] 完整数据({messageData.Length}字节): {string.Join(" ", allHexBytes)}");
            }

            // 同时显示Base64编码，便于分析
            Console.WriteLine($"[DEBUG] Base64编码: {Convert.ToBase64String(messageData)}");

            // 尝试显示为文本（如果可能）
            try
            {
                var textAttempt = System.Text.Encoding.UTF8.GetString(messageData);
                if (!string.IsNullOrEmpty(textAttempt) && textAttempt.All(c => c >= 32 && c <= 126 || char.IsWhiteSpace(c)))
                {
                    Console.WriteLine($"[DEBUG] 可能的文本内容: {textAttempt}");
                }
            }
            catch
            {
                // 忽略文本转换错误
            }
            
            try
            {
                // 首先尝试解析为ServerMessage（最可能的格式）
                Console.WriteLine("[DEBUG] 尝试解析ServerMessage...");
                try
                {
                    ProcessServerMessage(messageData);
                    return; // 成功处理ServerMessage，直接返回
                }
                catch (Google.Protobuf.InvalidProtocolBufferException ex)
                {
                    Console.WriteLine($"[DEBUG] 不是ServerMessage格式: {ex.Message}");
                }
                
                // 如果不是ServerMessage，尝试解析为WebSocketMessage
                Console.WriteLine("[DEBUG] 尝试解析WebSocketMessage...");
                using (var stream = new MemoryStream(messageData))
                {
                    var wsMessage = WebSocketMessage.Parser.ParseFrom(stream);
                    Console.WriteLine($"[DEBUG] WebSocketMessage解析成功 - 类型: {wsMessage.Type}");
                    
                    switch (wsMessage.Type)
                    {
                        case MessageType.CommonData:
                            ProcessCommonData(wsMessage.Payload.ToByteArray(), messageData.Length);
                            break;
                        case MessageType.Message:
                            ProcessMessageData(wsMessage.Payload.ToByteArray(), messageData.Length);
                            break;
                        case MessageType.Heartbeat:
                            Console.WriteLine("[DEBUG] 收到心跳消息");
                            break;
                        case MessageType.Subscribe:
                            Console.WriteLine("[DEBUG] 收到订阅确认消息");
                            break;
                        default:
                            Console.WriteLine($"[DEBUG] 未知消息类型: {wsMessage.Type}");
                            break;
                    }
                }
            }
            catch (Google.Protobuf.InvalidProtocolBufferException)
            {
                Console.WriteLine("[DEBUG] 也不是WebSocketMessage，尝试其他格式...");
                // 尝试其他格式
                TryProcessDirectMessage(messageData, null);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 处理消息时发生未知错误: {ex.Message}");
                TryProcessDirectMessage(messageData, ex);
            }
        }

        private void ProcessMessageData(byte[] payload, int messageSize)
        {
            try
            {
                var messageData = Message.Parser.ParseFrom(payload);
                
                // 从payload中提取设备ID和数据类型
                var deviceId = messageData.Payload.ContainsKey("deviceId") ? messageData.Payload["deviceId"] : "Unknown";
                var dataType = messageData.Payload.ContainsKey("dataType") ? messageData.Payload["dataType"] : "MESSAGE_DATA";
                
                // 构建详细信息字符串
                var details = string.Join(", ", messageData.Payload.Select(kvp => $"{kvp.Key}={kvp.Value}"));
                
                DataReceived?.Invoke(this, new DataReceivedEventArgs
                {
                    DataType = dataType,
                    DeviceId = deviceId,
                    Details = details,
                    RawMessage = $"InsertDate: {messageData.InsertDate}, Payload: {details}",
                    MessageSize = messageSize
                });
            }
            catch (Exception ex)
            {
                DataReceived?.Invoke(this, new DataReceivedEventArgs
                {
                    DataType = "Error",
                    DeviceId = "N/A",
                    Details = $"Message解析错误: {ex.Message}",
                    RawMessage = $"Error: {ex.Message}",
                    MessageSize = messageSize
                });
            }
        }

        private void TryProcessDirectMessage(byte[] messageData, Exception originalException)
        {
            try
            {
                // 尝试直接解析为Message格式（不包装在WebSocketMessage中）
                var messageData_direct = Message.Parser.ParseFrom(messageData);
                ProcessDirectMessage(messageData_direct, messageData.Length);
            }
            catch (Exception)
            {
                // 如果直接解析Message也失败，尝试解析为CommonData
                try
                {
                    var commonData = CommonData.Parser.ParseFrom(messageData);
                    ProcessDirectCommonData(commonData, messageData.Length);
                }
                catch (Exception)
                {
                    // 所有解析都失败，报告原始错误
                    DataReceived?.Invoke(this, new DataReceivedEventArgs
                    {
                        DataType = "Error",
                        DeviceId = "N/A",
                        Details = $"所有格式解析失败: {originalException.Message}",
                        RawMessage = $"Error: {originalException.Message}",
                        MessageSize = messageData.Length
                    });
                }
            }
        }

        private void ProcessDirectMessage(Message messageData, int messageSize)
        {
            try
            {
                var deviceId = messageData.Payload.ContainsKey("deviceId") ? messageData.Payload["deviceId"] : "Direct_Unknown";
                var dataType = messageData.Payload.ContainsKey("dataType") ? messageData.Payload["dataType"] : "DIRECT_MESSAGE";
                
                var details = string.Join(", ", messageData.Payload.Select(kvp => $"{kvp.Key}={kvp.Value}"));
                
                DataReceived?.Invoke(this, new DataReceivedEventArgs
                {
                    DataType = $"Direct_{dataType}",
                    DeviceId = deviceId,
                    Details = details,
                    RawMessage = $"Direct Message - InsertDate: {messageData.InsertDate}, Payload: {details}",
                    MessageSize = messageSize
                });
            }
            catch (Exception ex)
            {
                DataReceived?.Invoke(this, new DataReceivedEventArgs
                {
                    DataType = "Error",
                    DeviceId = "N/A",
                    Details = $"直接Message解析错误: {ex.Message}",
                    RawMessage = $"Error: {ex.Message}",
                    MessageSize = messageSize
                });
            }
        }

        private void ProcessDirectCommonData(CommonData commonData, int messageSize)
        {
            try
            {
                foreach (var status in commonData.StatusList)
                {
                    var details = string.Join(", ", status.Fields.Select(f => $"{f.Key}={GetFieldValue(f)}"));
                    
                    DataReceived?.Invoke(this, new DataReceivedEventArgs
                    {
                        DataType = $"Direct_{commonData.DataType}",
                        DeviceId = status.Id,
                        Details = details,
                        RawMessage = $"Direct CommonData - DataType: {commonData.DataType}, Date: {commonData.InsertDate}, Device: {status.Id}",
                        MessageSize = messageSize
                    });
                }
            }
            catch (Exception ex)
            {
                DataReceived?.Invoke(this, new DataReceivedEventArgs
                {
                    DataType = "Error",
                    DeviceId = "N/A",
                    Details = $"直接CommonData解析错误: {ex.Message}",
                    RawMessage = $"Error: {ex.Message}",
                    MessageSize = messageSize
                });
            }
        }

        private void ProcessCommonData(byte[] payload, int messageSize)
        {
            try
            {
                var commonData = CommonData.Parser.ParseFrom(payload);
                
                foreach (var status in commonData.StatusList)
                {
                    var details = string.Join(", ", status.Fields.Select(f => $"{f.Key}={GetFieldValue(f)}"));
                    
                    DataReceived?.Invoke(this, new DataReceivedEventArgs
                    {
                        DataType = commonData.DataType,
                        DeviceId = status.Id,
                        Details = details,
                        RawMessage = $"CommonData - DataType: {commonData.DataType}, Date: {commonData.InsertDate}, Device: {status.Id}",
                        MessageSize = messageSize
                    });
                }
            }
            catch (Exception ex)
            {
                DataReceived?.Invoke(this, new DataReceivedEventArgs
                {
                    DataType = "Error",
                    DeviceId = "N/A",
                    Details = $"CommonData解析错误: {ex.Message}",
                    RawMessage = $"Error: {ex.Message}",
                    MessageSize = messageSize
                });
            }
        }

        private string GetFieldValue(DataField field)
        {
            return field.Type switch
            {
                0 => field.StringValue,
                1 => field.DoubleValue.ToString("F2"),
                2 => field.IntValue.ToString(),
                _ => "Unknown type"
            };
        }

        public async Task DisconnectAsync()
        {
            try
            {
                Console.WriteLine("[DEBUG] DisconnectAsync开始执行...");
                
                if (_cancellationTokenSource != null)
                {
                    Console.WriteLine("[DEBUG] 取消接收循环...");
                    _cancellationTokenSource.Cancel();
                }
                
                if (_webSocket?.State == WebSocketState.Open)
                {
                    Console.WriteLine("[DEBUG] 关闭WebSocket连接...");
                    await _webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Client disconnecting", CancellationToken.None);
                }
                
                Console.WriteLine("[DEBUG] 清理资源...");
                _webSocket?.Dispose();
                _cancellationTokenSource?.Dispose();
                
                Console.WriteLine("[DEBUG] 触发连接状态变化事件...");
                ConnectionStatusChanged?.Invoke(this, false);
                
                Console.WriteLine("[DEBUG] DisconnectAsync执行完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] DisconnectAsync异常: {ex.Message}");
                
                // 确保即使出现异常也要清理资源和更新状态
                try
                {
                    _webSocket?.Dispose();
                    _cancellationTokenSource?.Dispose();
                    ConnectionStatusChanged?.Invoke(this, false);
                }
                catch (Exception cleanupEx)
                {
                    Console.WriteLine($"[ERROR] 清理资源时异常: {cleanupEx.Message}");
                }
            }
        }

        // 添加测试Message格式的方法
        public void TestMessageFormat()
        {
            try
            {
                // 创建测试用的Message数据
                var testMessage = new Message
                {
                    InsertDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    Payload = {
                        { "dataType", "TEST_MESSAGE" },
                        { "deviceId", "TEST_DEVICE_001" },
                        { "status", "active" },
                        { "value1", "123.45" },
                        { "value2", "test_string" }
                    }
                };

                // 编码为二进制
                var messageBytes = testMessage.ToByteArray();
                
                // 模拟接收到这个消息
                ProcessDirectMessage(testMessage, messageBytes.Length);
                
                Console.WriteLine("Message格式测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Message格式测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送加密消息（如果启用加密）
        /// </summary>
        private async Task SendEncryptedMessage(byte[] data)
        {
            try
            {
                byte[] finalData = data;

                if (_encryptionEnabled && _encryptionUtils != null)
                {
                    finalData = _encryptionUtils.Encrypt(data);
                    Console.WriteLine($"[DEBUG] 消息已加密，原始大小: {data.Length}, 加密后大小: {finalData.Length}");
                }

                if (_webSocket?.State == WebSocketState.Open)
                {
                    await _webSocket.SendAsync(
                        new ArraySegment<byte>(finalData),
                        WebSocketMessageType.Binary,
                        true,
                        _cancellationTokenSource?.Token ?? CancellationToken.None
                    );
                }
                else
                {
                    Console.WriteLine("[ERROR] WebSocket未连接，无法发送消息");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 发送加密消息失败: {ex.Message}");
                throw;
            }
        }

        // 添加心跳发送方法
        public async Task SendHeartbeat()
        {
            try
            {
                if (_webSocket?.State != WebSocketState.Open) return;
                
                var heartbeatMessage = new WebSocketMessage
                {
                    Type = MessageType.Heartbeat,
                    Payload = Google.Protobuf.ByteString.Empty,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                    MessageId = $"heartbeat_{Guid.NewGuid():N}"
                };
                
                var messageBytes = heartbeatMessage.ToByteArray();

                await SendEncryptedMessage(messageBytes);
                Console.WriteLine("[DEBUG] 心跳消息已发送");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 发送心跳失败: {ex.Message}");
            }
        }

        // 添加发送ClientRequest的方法
        public async Task SendClientRequest(string userId, string sceneId)
        {
            try
            {
                Console.WriteLine($"[DEBUG] 开始创建ClientRequest - UserId: {userId}, SceneId: {sceneId}");
                
                var clientRequest = new ClientRequest
                {
                    UserId = userId,
                    SceneId = sceneId
                };

                // 验证创建的对象
                Console.WriteLine($"[DEBUG] 创建的ClientRequest - UserId: '{clientRequest.UserId}', SceneId: '{clientRequest.SceneId}'");

                var requestBytes = clientRequest.ToByteArray();
                Console.WriteLine($"[DEBUG] 序列化后的数据大小: {requestBytes.Length} bytes");
                
                // 显示发送的数据内容
                var hexString = string.Join(" ", requestBytes.Select(b => b.ToString("X2")));
                Console.WriteLine($"[DEBUG] 发送的二进制数据: {hexString}");
                Console.WriteLine($"[DEBUG] 发送的Base64数据: {Convert.ToBase64String(requestBytes)}");

                // 验证序列化是否正确 - 重新解析一遍
                try
                {
                    var testParse = ClientRequest.Parser.ParseFrom(requestBytes);
                    Console.WriteLine($"[DEBUG] 验证解析 - UserId: '{testParse.UserId}', SceneId: '{testParse.SceneId}'");
                }
                catch (Exception parseEx)
                {
                    Console.WriteLine($"[ERROR] 验证解析失败: {parseEx.Message}");
                }

                await SendEncryptedMessage(requestBytes);
                Console.WriteLine("[DEBUG] ClientRequest发送成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 发送ClientRequest失败: {ex.Message}");
                Console.WriteLine($"[ERROR] 异常详情: {ex}");
            }
        }

        // 添加处理ServerMessage的方法
        private void ProcessServerMessage(byte[] messageData)
        {
            try
            {
                using (var stream = new MemoryStream(messageData))
                {
                    var serverMessage = ServerMessage.Parser.ParseFrom(stream);
                    Console.WriteLine($"[DEBUG] 收到ServerMessage - Topic: {serverMessage.TopicName}");
                    Console.WriteLine($"[DEBUG] Payload: {serverMessage.Payload}");
                  
                        // 处理其他topic数据
                        ProcessGenericServerMessage(serverMessage);

                    // 重要：触发事件通知UI显示数据
                    DataReceived?.Invoke(this, new DataReceivedEventArgs
                    {
                        DataType = $"ServerMessage_{serverMessage.TopicName}",
                        DeviceId = serverMessage.TopicName,
                        Details = serverMessage.Payload,
                        RawMessage = $"Topic: {serverMessage.TopicName}, Payload: {serverMessage.Payload}",
                        MessageSize = messageData.Length
                    });
                    
                    Console.WriteLine("[DEBUG] ServerMessage处理完成，已触发DataReceived事件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 解析ServerMessage失败: {ex.Message}");
                
                // 如果解析失败，仍然触发事件显示原始数据
                DataReceived?.Invoke(this, new DataReceivedEventArgs
                {
                    DataType = "ServerMessage_ParseError",
                    DeviceId = "Unknown",
                    Details = $"解析失败: {ex.Message}",
                    RawMessage = $"Raw bytes: {Convert.ToBase64String(messageData)}",
                    MessageSize = messageData.Length
                });
            }
        }

        private void ProcessGenericServerMessage(ServerMessage serverMessage)
        {
            Console.WriteLine($"[DEBUG] 处理通用ServerMessage - Topic: {serverMessage.TopicName}");
            
            // 可以根据不同的topic做不同的处理
            switch (serverMessage.TopicName)
            {
                case "dev-loc":
                    Console.WriteLine("[DEBUG] 处理设备位置数据");
                    break;
                case "dcv-base":
                    Console.WriteLine("[DEBUG] 处理DCV基础数据");
                    break;
                default:
                    Console.WriteLine($"[DEBUG] 处理未知topic: {serverMessage.TopicName}");
                    break;
            }
        }
    }
}





















