<Window x:Class="CargoWebSocketClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="孪生数据实时监控" Height="600" Width="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 连接状态和控制按钮 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <TextBlock Text="服务器地址:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox x:Name="ServerUrlTextBox" Text="ws://localhost:8080" Width="200" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBlock Text="场景:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <ComboBox x:Name="SceneComboBox" Width="100" VerticalAlignment="Center" Margin="0,0,10,0">
                <ComboBoxItem Content="scene1" IsSelected="True"/>
                <ComboBoxItem Content="scene2"/>
            </ComboBox>
            <TextBlock x:Name="StatusText" Text="未连接" Foreground="Red" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <Button x:Name="ConnectButton" Content="连接" Margin="10,0" Padding="10,5" Click="ConnectButton_Click"/>
            <Button x:Name="DisconnectButton" Content="断开" Margin="10,0" Padding="10,5" Click="DisconnectButton_Click"/>
            <Button x:Name="ClearButton" Content="清空数据" Margin="10,0" Padding="10,5" Click="ClearButton_Click"/>
        </StackPanel>
        
        <!-- 统计信息 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="10">
            <TextBlock Text="总消息数: " FontWeight="Bold"/>
            <TextBlock x:Name="TotalCountText" Text="0" Margin="0,0,20,0"/>
            <TextBlock Text="最后消息大小: " FontWeight="Bold"/>
            <TextBlock x:Name="MessageSizeText" Text="0 bytes" Margin="0,0,20,0"/>
            <TextBlock Text="连接时间: " FontWeight="Bold"/>
            <TextBlock x:Name="ConnectTimeText" Text="--"/>
        </StackPanel>
        
        <!-- 数据显示区域 -->
        <TabControl Grid.Row="2" Margin="10">
            <TabItem Header="实时数据">
                <ListView x:Name="DataListView" ScrollViewer.HorizontalScrollBarVisibility="Auto">
                    <ListView.View>
                        <GridView>
                            <GridViewColumn Header="时间" Width="150" DisplayMemberBinding="{Binding Timestamp}"/>
                            <GridViewColumn Header="数据类型" Width="120" DisplayMemberBinding="{Binding DataType}"/>
                            <GridViewColumn Header="设备ID" Width="150" DisplayMemberBinding="{Binding DeviceId}"/>
                            <GridViewColumn Header="详细信息" Width="400" DisplayMemberBinding="{Binding Details}"/>
                        </GridView>
                    </ListView.View>
                </ListView>
            </TabItem>
            <TabItem Header="原始消息">
                <TextBox x:Name="RawMessageTextBox" IsReadOnly="True" 
                         VerticalScrollBarVisibility="Auto" 
                         HorizontalScrollBarVisibility="Auto"
                         FontFamily="Consolas" FontSize="10"/>
            </TabItem>
        </TabControl>
        
        <!-- 数据详情弹框 -->
        <Grid x:Name="DetailModalOverlay" Grid.RowSpan="3" Background="#80000000" Visibility="Collapsed">
            <Border Background="White" BorderBrush="Gray" BorderThickness="2" 
                    MaxWidth="600" MaxHeight="400" Margin="50">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" x:Name="DetailModalTitle" Text="数据详情" 
                              FontSize="16" FontWeight="Bold" Margin="10" 
                              HorizontalAlignment="Center"/>
                    
                    <ScrollViewer Grid.Row="1" Margin="10">
                        <TextBlock x:Name="DetailModalContent" TextWrapping="Wrap" 
                                  FontFamily="Consolas" FontSize="12"/>
                    </ScrollViewer>
                    
                    <StackPanel Grid.Row="2" Orientation="Horizontal" 
                               HorizontalAlignment="Center" Margin="10">
                        <Button Content="关闭" Padding="20,5" Click="CloseDetailModal_Click"/>
                        <TextBlock Text="(或按 ESC 键关闭)" VerticalAlignment="Center" 
                                  Margin="10,0,0,0" FontSize="10" Foreground="Gray"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>


