<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>货物数据实时监控</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .data-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background-color: #f2f2f2; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { padding: 15px; border: 1px solid #ddd; border-radius: 5px; flex: 1; }
        .new-row { animation: highlight 2s ease-in-out; }
        @keyframes highlight { from { background-color: #fff3cd; } to { background-color: transparent; } }
        
        /* Topic监控样式 */
        .topic-monitor { 
            background-color: #000; 
            color: #00ff00; 
            font-family: 'Courier New', monospace; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
        }
        .topic-monitor h3 { 
            color: #00ff00; 
            margin-top: 0; 
            text-align: center;
            border-bottom: 1px solid #00ff00;
            padding-bottom: 10px;
        }
        .topic-item { 
            display: flex; 
            align-items: center; 
            justify-content: end;
            margin: 2px 0; 
            font-size: 12px;
            line-height: 1.2;
        }
        .topic-progress { 
            width: 1000px; 
            margin-right: 10px; 
            /* display: flex;
            align-items: center;
            justify-content: flex-start; */
        }
        .progress-bars { 
            display: inline-block;
            width: 100%;
            font-family: 'Courier New', monospace;
            color: #00ff00; 
            margin-right: 5px;
            font-size: 10px;
            letter-spacing: 1px;
            white-space: pre;
            text-align: right; /* 添加右对齐 */
            min-width: 150px;
        }
        .progress-count { 
            color: #00ff00; 
            font-size: 10px;
            min-width: 20px;
        }
        .topic-name { 
            width: 100px;
            text-align: left; 
            color: #00ff00;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-right: 20px;
        }
        .topic-name:hover {
            background-color: #222;
        }
        .topic-name.selected {
            background-color: #333;
            color: #ffff00;
            font-weight: bold;
        }
        .topic-time { 
            width: 100px; 
            text-align: right; 
            color: #ffff00;
            flex: 1;
        }
        .topic-new { 
            animation: flash 1s ease-in-out; 
        }
        @keyframes flash { 
            0%, 100% { background-color: transparent; } 
            50% { background-color: #004400; } 
        }

        /* 数据展示区域 */
        .data-sections { 
            display: grid; 
            grid-template-columns: 1fr 1fr 1fr; 
            gap: 20px; 
            margin: 20px 0; 
        }
        .data-section { 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            padding: 15px; 
            background-color: white;
        }
        .data-section h3 { 
            margin-top: 0; 
            color: #333; 
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .data-item { 
            padding: 10px; 
            margin: 5px 0; 
            border: 1px solid #eee; 
            border-radius: 3px; 
            background-color: #f9f9f9;
        }
        .new-item { 
            animation: newItemHighlight 2s ease-in-out; 
        }
        @keyframes newItemHighlight { 
            from { background-color: #fff3cd; } 
            to { background-color: #f9f9f9; } 
        }

        /* 弹框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        .modal-content {
            background-color: #000;
            color: #00ff00;
            margin: 5% auto;
            padding: 20px;
            border: 2px solid #00ff00;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            max-height: 80%;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #00ff00;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
        }
        .close {
            color: #ff0000;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #ff4444;
        }
        .modal-data {
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.4;
        }

        /* 消息展示区域样式 */
        .message-display {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }
        .message-display h3 {
            color: #007bff;
            margin-top: 0;
            text-align: center;
            border-bottom: 1px solid #007bff;
            padding-bottom: 10px;
        }
        #messageContent {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 15px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>货物数据实时监控系统</h1>
        
        <div id="status" class="status disconnected">
            连接状态: 未连接
        </div>
        
        <div class="controls">
            <label for="scenarioSelect">选择场景:</label>
            <select id="scenarioSelect" onchange="switchScenario()">
                <option value="">请选择场景...</option>
            </select>
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开</button>
            <button onclick="clearData()">清空数据</button>
            <button onclick="exportData()">导出数据</button>
            <button onclick="openModal()">查看详情</button>
        </div>
        
        <div class="stats">
            <!-- <div class="stat-card">
                <h3>总数据量</h3>
                <div id="totalCount">0</div>
            </div>
            <div class="stat-card">
                <h3>入境数量</h3>
                <div id="entryCount">0</div>
            </div>
            <div class="stat-card">
                <h3>出境数量</h3>
                <div id="exitCount">0</div>
            </div>
            <div class="stat-card">
                <h3>CSP时间数据</h3>
                <div id="cspTimeCount">0</div>
            </div> -->
            <div class="stat-card">
                <h3>设备位置数据</h3>
                <div id="devLocCount">0</div>
            </div>
            <div class="stat-card">
                <h3>DCV基础数据</h3>
                <div id="dcvBaseCount">0</div>
            </div>
            <div class="stat-card">
                <h3>消息大小</h3>
                <div id="messageSize">0 bytes</div>
            </div>
        </div>

        <!-- 在Topic监控区域之前添加消息展示区域 -->
        <!-- <div class="message-display">
            <h3>实时消息推送</h3>
            <div id="messageContent">等待消息推送...</div>
        </div> -->

        <!-- Topic监控区域 -->
        <div class="topic-monitor">
            <h3>Topic 推送监控</h3>
            <div id="topicList"></div>
        </div>

        <!-- 数据展示区域 -->
        <div class="data-sections">
            <div class="data-section">
                <h3 id="dataSection1Title">时间数据</h3>
                <div id="cspTimeData"></div>
            </div>
            <div class="data-section">
                <h3 id="dataSection2Title">位置数据</h3>
                <div id="devLocData"></div>
            </div>
            <div class="data-section">
                <h3 id="dataSection3Title">基础数据</h3>
                <div id="dcvBaseData"></div>
            </div>
        </div>
        
        <table class="data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>时间戳</th>
                    <th>代码</th>
                    <th>进出类型</th>
                    <th>进出标识</th>
                    <th>货物名称</th>
                    <th>原产国</th>
                    <th>数量</th>
                    <th>检查点</th>
                    <th>车牌号</th>
                </tr>
            </thead>
            <tbody id="dataTableBody">
            </tbody>
        </table>
    </div>

    <!-- 弹框 -->
    <div id="dataModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="modal-title" id="modalTitle">Topic数据详情</span>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-data" id="modalData">
                暂无数据
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/protobufjs@7.2.5/dist/protobuf.min.js"></script>
    <script src="scenario-config.js"></script>
    <script src="client.js"></script>
</body>
</html>












