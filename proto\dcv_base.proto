syntax = "proto3";

package cargo;

// dcv_base状态数据
message DcvBaseStatus {
  string id = 1;
  string fuelType = 2;
  double fuelRemainingPercentage = 3;
  string jobType = 4;
  string workStatus = 5;
  double energy = 6;
  double energyDelta = 7;
  string workLegendStatus = 8;
  string jobMode = 9;
  string cntrNo1 = 10;
  string cntrOwnerCd1 = 11;
  string cntrSize1 = 12;
  string cntrType1 = 13;
  string cntrDgFg1 = 14;
  string cntrOogFg1 = 15;
  string cntrNo2 = 16;
  string cntrOwnerCd2 = 17;
  string cntrSize2 = 18;
  string cntrType2 = 19;
  string cntrDgFg2 = 20;
  string cntrOogFg2 = 21;
  double agvLocXStart = 22;
  double agvLocYStart = 23;
  double agvLocXEnd = 24;
  double agvLocYEnd = 25;
  double agvDriveAngleStart = 26;
  double agvDriveAngleEnd = 27;
}

// dcv_base数据
message DcvBaseData {
  string insertDate = 1;
  repeated DcvBaseStatus statusList = 2;
}