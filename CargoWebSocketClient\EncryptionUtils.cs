using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace CargoWebSocketClient
{
    /// <summary>
    /// AES-GCM 加密工具类 - C# 实现
    /// 与Node.js版本保持兼容
    /// </summary>
    public class EncryptionUtils
    {
        private readonly byte[] _secretKey;
        private const string AAD = "websocket-protobuf";

        public EncryptionUtils(byte[] secretKey = null)
        {
            _secretKey = secretKey ?? GenerateRandomKey();
        }

        /// <summary>
        /// 生成32字节随机密钥
        /// </summary>
        public static byte[] GenerateRandomKey()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var key = new byte[32]; // AES-256
                rng.GetBytes(key);
                return key;
            }
        }

        /// <summary>
        /// 加密Protocol Buffer数据
        /// </summary>
        /// <param name="data">原始protobuf数据</param>
        /// <returns>加密后的数据 (IV + AuthTag + EncryptedData)</returns>
        public byte[] Encrypt(byte[] data)
        {
            try
            {
                using (var aes = new AesGcm(_secretKey))
                {
                    // 生成12字节IV
                    var iv = new byte[12];
                    RandomNumberGenerator.Fill(iv);

                    // 准备输出缓冲区
                    var ciphertext = new byte[data.Length];
                    var authTag = new byte[16];

                    // 附加认证数据
                    var aad = Encoding.UTF8.GetBytes(AAD);

                    // 执行加密
                    aes.Encrypt(iv, data, ciphertext, authTag, aad);

                    // 组合结果: IV(12) + AuthTag(16) + EncryptedData
                    var result = new byte[iv.Length + authTag.Length + ciphertext.Length];
                    Buffer.BlockCopy(iv, 0, result, 0, iv.Length);
                    Buffer.BlockCopy(authTag, 0, result, iv.Length, authTag.Length);
                    Buffer.BlockCopy(ciphertext, 0, result, iv.Length + authTag.Length, ciphertext.Length);

                    return result;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 加密失败: {ex.Message}");
                throw new InvalidOperationException("数据加密失败", ex);
            }
        }

        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <returns>解密后的protobuf数据</returns>
        public byte[] Decrypt(byte[] encryptedData)
        {
            try
            {
                if (encryptedData.Length < 28) // 12(IV) + 16(AuthTag) = 28
                {
                    throw new ArgumentException("加密数据格式错误");
                }

                using (var aes = new AesGcm(_secretKey))
                {
                    // 提取组件
                    var iv = new byte[12];
                    var authTag = new byte[16];
                    var ciphertext = new byte[encryptedData.Length - 28];

                    Buffer.BlockCopy(encryptedData, 0, iv, 0, 12);
                    Buffer.BlockCopy(encryptedData, 12, authTag, 0, 16);
                    Buffer.BlockCopy(encryptedData, 28, ciphertext, 0, ciphertext.Length);

                    // 准备输出缓冲区
                    var plaintext = new byte[ciphertext.Length];
                    var aad = Encoding.UTF8.GetBytes(AAD);

                    // 执行解密
                    aes.Decrypt(iv, ciphertext, authTag, plaintext, aad);

                    return plaintext;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 解密失败: {ex.Message}");
                throw new InvalidOperationException("数据解密失败", ex);
            }
        }

        /// <summary>
        /// 生成RSA密钥对用于密钥交换
        /// </summary>
        public static (string publicKey, string privateKey) GenerateRSAKeyPair()
        {
            using (var rsa = RSA.Create(2048))
            {
                var publicKey = rsa.ExportRSAPublicKeyPem();
                var privateKey = rsa.ExportRSAPrivateKeyPem();
                return (publicKey, privateKey);
            }
        }

        /// <summary>
        /// 使用RSA公钥加密AES密钥
        /// </summary>
        public static byte[] EncryptKey(byte[] aesKey, string publicKeyPem)
        {
            using (var rsa = RSA.Create())
            {
                rsa.ImportFromPem(publicKeyPem);
                return rsa.Encrypt(aesKey, RSAEncryptionPadding.OaepSHA256);
            }
        }

        /// <summary>
        /// 使用RSA私钥解密AES密钥
        /// </summary>
        public static byte[] DecryptKey(byte[] encryptedKey, string privateKeyPem)
        {
            using (var rsa = RSA.Create())
            {
                rsa.ImportFromPem(privateKeyPem);
                return rsa.Decrypt(encryptedKey, RSAEncryptionPadding.OaepSHA256);
            }
        }

        /// <summary>
        /// 获取当前密钥
        /// </summary>
        public byte[] GetKey() => (byte[])_secretKey.Clone();
    }
}
