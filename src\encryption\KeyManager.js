import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';

/**
 * 密钥管理器 - 处理密钥生成、存储和分发
 */
class KeyManager {
    constructor() {
        this.keyStorage = new Map(); // 存储客户端密钥
        this.serverKeyPair = null;
    }

    /**
     * 初始化服务器密钥对
     */
    async initializeServerKeys() {
        try {
            // 尝试从文件加载现有密钥
            const keyPath = path.join(process.cwd(), 'keys');
            await fs.mkdir(keyPath, { recursive: true });
            
            const publicKeyPath = path.join(keyPath, 'server_public.pem');
            const privateKeyPath = path.join(keyPath, 'server_private.pem');
            
            try {
                const publicKey = await fs.readFile(publicKeyPath, 'utf8');
                const privateKey = await fs.readFile(privateKeyPath, 'utf8');
                
                this.serverKeyPair = { publicKey, privateKey };
                console.log('[INFO] 服务器密钥对已从文件加载');
            } catch (fileError) {
                // 文件不存在，生成新密钥对
                console.log('[INFO] 生成新的服务器密钥对...');
                this.serverKeyPair = crypto.generateKeyPairSync('rsa', {
                    modulusLength: 2048,
                    publicKeyEncoding: {
                        type: 'spki',
                        format: 'pem'
                    },
                    privateKeyEncoding: {
                        type: 'pkcs8',
                        format: 'pem'
                    }
                });
                
                // 保存密钥到文件
                await fs.writeFile(publicKeyPath, this.serverKeyPair.publicKey);
                await fs.writeFile(privateKeyPath, this.serverKeyPair.privateKey);
                console.log('[INFO] 服务器密钥对已保存到文件');
            }
        } catch (error) {
            console.error('[ERROR] 初始化服务器密钥失败:', error);
            throw error;
        }
    }

    /**
     * 为新客户端生成AES密钥
     */
    generateClientKey(clientId) {
        const aesKey = crypto.randomBytes(32); // AES-256密钥
        this.keyStorage.set(clientId, aesKey);
        console.log(`[INFO] 为客户端 ${clientId} 生成了新的AES密钥`);
        return aesKey;
    }

    /**
     * 获取客户端密钥
     */
    getClientKey(clientId) {
        return this.keyStorage.get(clientId);
    }

    /**
     * 使用RSA加密AES密钥发送给客户端
     */
    encryptKeyForClient(aesKey) {
        if (!this.serverKeyPair) {
            throw new Error('服务器密钥对未初始化');
        }
        
        return crypto.publicEncrypt(this.serverKeyPair.publicKey, aesKey);
    }

    /**
     * 获取服务器公钥（用于客户端验证）
     */
    getServerPublicKey() {
        return this.serverKeyPair?.publicKey;
    }

    /**
     * 密钥交换协议 - 处理客户端密钥请求
     */
    handleKeyExchange(clientId) {
        try {
            // 1. 生成AES密钥
            const aesKey = this.generateClientKey(clientId);
            
            // 2. 使用RSA加密AES密钥
            const encryptedKey = this.encryptKeyForClient(aesKey);
            
            // 3. 返回密钥交换响应
            return {
                encryptedKey: encryptedKey.toString('base64'),
                serverPublicKey: this.getServerPublicKey(),
                keyId: `key_${clientId}_${Date.now()}`
            };
        } catch (error) {
            console.error(`[ERROR] 客户端 ${clientId} 密钥交换失败:`, error);
            throw error;
        }
    }

    /**
     * 移除客户端密钥
     */
    removeClientKey(clientId) {
        const removed = this.keyStorage.delete(clientId);
        if (removed) {
            console.log(`[INFO] 已移除客户端 ${clientId} 的密钥`);
        }
        return removed;
    }

    /**
     * 获取密钥统计信息
     */
    getKeyStats() {
        return {
            totalClients: this.keyStorage.size,
            serverKeyInitialized: !!this.serverKeyPair
        };
    }
}

export default KeyManager;
