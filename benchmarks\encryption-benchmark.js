import crypto from 'crypto';
import EncryptionUtils from '../src/encryption/EncryptionUtils.js';

/**
 * 加密性能基准测试
 */
class EncryptionBenchmark {
    constructor() {
        this.encryptionUtils = new EncryptionUtils();
        this.results = [];
    }

    /**
     * 生成测试数据
     */
    generateTestData(size) {
        return crypto.randomBytes(size);
    }

    /**
     * 测试AES-GCM性能
     */
    async benchmarkAESGCM(dataSize, iterations = 1000) {
        console.log(`\n=== AES-256-GCM 性能测试 ===`);
        console.log(`数据大小: ${dataSize} bytes, 迭代次数: ${iterations}`);
        
        const testData = this.generateTestData(dataSize);
        
        // 加密性能测试
        const encryptStart = process.hrtime.bigint();
        const encryptedResults = [];
        
        for (let i = 0; i < iterations; i++) {
            const encrypted = this.encryptionUtils.encrypt(testData);
            encryptedResults.push(encrypted);
        }
        
        const encryptEnd = process.hrtime.bigint();
        const encryptTime = Number(encryptEnd - encryptStart) / 1000000; // 转换为毫秒
        
        // 解密性能测试
        const decryptStart = process.hrtime.bigint();
        
        for (let i = 0; i < iterations; i++) {
            this.encryptionUtils.decrypt(encryptedResults[i]);
        }
        
        const decryptEnd = process.hrtime.bigint();
        const decryptTime = Number(decryptEnd - decryptStart) / 1000000;
        
        // 计算性能指标
        const totalDataMB = (dataSize * iterations) / (1024 * 1024);
        const encryptThroughput = totalDataMB / (encryptTime / 1000);
        const decryptThroughput = totalDataMB / (decryptTime / 1000);
        
        const result = {
            dataSize,
            iterations,
            encryptTime: encryptTime.toFixed(2),
            decryptTime: decryptTime.toFixed(2),
            encryptThroughput: encryptThroughput.toFixed(2),
            decryptThroughput: decryptThroughput.toFixed(2),
            avgEncryptLatency: (encryptTime / iterations).toFixed(3),
            avgDecryptLatency: (decryptTime / iterations).toFixed(3),
            overhead: ((encryptedResults[0].length - dataSize) / dataSize * 100).toFixed(1)
        };
        
        console.log(`加密时间: ${result.encryptTime}ms`);
        console.log(`解密时间: ${result.decryptTime}ms`);
        console.log(`加密吞吐量: ${result.encryptThroughput} MB/s`);
        console.log(`解密吞吐量: ${result.decryptThroughput} MB/s`);
        console.log(`平均加密延迟: ${result.avgEncryptLatency}ms`);
        console.log(`平均解密延迟: ${result.avgDecryptLatency}ms`);
        console.log(`数据膨胀率: ${result.overhead}%`);
        
        this.results.push(result);
        return result;
    }

    /**
     * 对比不同加密算法性能
     */
    async compareAlgorithms(dataSize = 1024) {
        console.log(`\n=== 加密算法性能对比 (${dataSize} bytes) ===`);
        
        const testData = this.generateTestData(dataSize);
        const iterations = 10000;
        
        // AES-256-GCM
        const gcmStart = process.hrtime.bigint();
        for (let i = 0; i < iterations; i++) {
            const encrypted = this.encryptionUtils.encrypt(testData);
            this.encryptionUtils.decrypt(encrypted);
        }
        const gcmTime = Number(process.hrtime.bigint() - gcmStart) / 1000000;
        
        // AES-256-CBC (对比)
        const cbcStart = process.hrtime.bigint();
        for (let i = 0; i < iterations; i++) {
            const iv = crypto.randomBytes(16);
            const cipher = crypto.createCipheriv('aes-256-cbc', this.encryptionUtils.secretKey, iv);
            const encrypted = Buffer.concat([cipher.update(testData), cipher.final()]);

            const decipher = crypto.createDecipheriv('aes-256-cbc', this.encryptionUtils.secretKey, iv);
            Buffer.concat([decipher.update(encrypted), decipher.final()]);
        }
        const cbcTime = Number(process.hrtime.bigint() - cbcStart) / 1000000;
        
        // ChaCha20-Poly1305 (如果支持)
        let chachaTime = 'N/A';
        try {
            const chachaStart = process.hrtime.bigint();
            for (let i = 0; i < iterations; i++) {
                const iv = crypto.randomBytes(12);
                const cipher = crypto.createCipheriv('chacha20-poly1305', this.encryptionUtils.secretKey, iv);
                const encrypted = Buffer.concat([cipher.update(testData), cipher.final()]);

                const decipher = crypto.createDecipheriv('chacha20-poly1305', this.encryptionUtils.secretKey, iv);
                Buffer.concat([decipher.update(encrypted), decipher.final()]);
            }
            chachaTime = (Number(process.hrtime.bigint() - chachaStart) / 1000000).toFixed(2);
        } catch (error) {
            chachaTime = '不支持';
        }
        
        console.log(`AES-256-GCM: ${gcmTime.toFixed(2)}ms`);
        console.log(`AES-256-CBC: ${cbcTime.toFixed(2)}ms`);
        console.log(`ChaCha20-Poly1305: ${chachaTime}ms`);
        
        return {
            'AES-256-GCM': gcmTime,
            'AES-256-CBC': cbcTime,
            'ChaCha20-Poly1305': chachaTime
        };
    }

    /**
     * 测试不同数据大小的性能
     */
    async benchmarkDifferentSizes() {
        console.log(`\n=== 不同数据大小性能测试 ===`);
        
        const sizes = [
            { name: '小消息 (64B)', size: 64, iterations: 50000 },
            { name: '中等消息 (1KB)', size: 1024, iterations: 10000 },
            { name: '大消息 (10KB)', size: 10240, iterations: 1000 },
            { name: '超大消息 (100KB)', size: 102400, iterations: 100 },
            { name: '巨型消息 (1MB)', size: 1048576, iterations: 10 }
        ];
        
        for (const test of sizes) {
            console.log(`\n--- ${test.name} ---`);
            await this.benchmarkAESGCM(test.size, test.iterations);
        }
    }

    /**
     * 内存使用测试
     */
    async benchmarkMemoryUsage() {
        console.log(`\n=== 内存使用测试 ===`);
        
        const initialMemory = process.memoryUsage();
        console.log(`初始内存使用: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        
        // 创建大量加密数据
        const testData = this.generateTestData(1024);
        const encryptedData = [];
        
        for (let i = 0; i < 10000; i++) {
            encryptedData.push(this.encryptionUtils.encrypt(testData));
        }
        
        const afterEncryption = process.memoryUsage();
        console.log(`加密后内存使用: ${(afterEncryption.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        console.log(`内存增长: ${((afterEncryption.heapUsed - initialMemory.heapUsed) / 1024 / 1024).toFixed(2)} MB`);
        
        // 解密所有数据
        for (const encrypted of encryptedData) {
            this.encryptionUtils.decrypt(encrypted);
        }
        
        const afterDecryption = process.memoryUsage();
        console.log(`解密后内存使用: ${(afterDecryption.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        
        // 强制垃圾回收
        if (global.gc) {
            global.gc();
            const afterGC = process.memoryUsage();
            console.log(`GC后内存使用: ${(afterGC.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        }
    }

    /**
     * 并发性能测试
     */
    async benchmarkConcurrency() {
        console.log(`\n=== 并发性能测试 ===`);
        
        const testData = this.generateTestData(1024);
        const concurrencyLevels = [1, 2, 4, 8, 16];
        
        for (const concurrency of concurrencyLevels) {
            const start = process.hrtime.bigint();
            const promises = [];
            
            for (let i = 0; i < concurrency; i++) {
                promises.push(this.runConcurrentTest(testData, 1000));
            }
            
            await Promise.all(promises);
            
            const time = Number(process.hrtime.bigint() - start) / 1000000;
            const totalOps = concurrency * 1000 * 2; // 加密+解密
            const opsPerSecond = (totalOps / (time / 1000)).toFixed(0);
            
            console.log(`并发度 ${concurrency}: ${time.toFixed(2)}ms, ${opsPerSecond} ops/s`);
        }
    }

    async runConcurrentTest(testData, iterations) {
        for (let i = 0; i < iterations; i++) {
            const encrypted = this.encryptionUtils.encrypt(testData);
            this.encryptionUtils.decrypt(encrypted);
        }
    }

    /**
     * 生成性能报告
     */
    generateReport() {
        console.log(`\n=== 性能测试报告 ===`);
        console.log(`测试时间: ${new Date().toISOString()}`);
        console.log(`Node.js版本: ${process.version}`);
        console.log(`平台: ${process.platform} ${process.arch}`);
        
        if (this.results.length > 0) {
            console.log(`\n最佳性能指标:`);
            const bestEncrypt = Math.max(...this.results.map(r => parseFloat(r.encryptThroughput)));
            const bestDecrypt = Math.max(...this.results.map(r => parseFloat(r.decryptThroughput)));
            console.log(`最高加密吞吐量: ${bestEncrypt} MB/s`);
            console.log(`最高解密吞吐量: ${bestDecrypt} MB/s`);
        }
    }
}

// 运行基准测试
async function runBenchmarks() {
    const benchmark = new EncryptionBenchmark();
    
    try {
        await benchmark.benchmarkDifferentSizes();
        await benchmark.compareAlgorithms();
        await benchmark.benchmarkMemoryUsage();
        await benchmark.benchmarkConcurrency();
        benchmark.generateReport();
    } catch (error) {
        console.error('基准测试失败:', error);
    }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
    runBenchmarks();
}

export default EncryptionBenchmark;
