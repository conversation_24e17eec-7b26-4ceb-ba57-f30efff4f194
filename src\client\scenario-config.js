// 场景配置文件 - 简化为基础配置，字段配置将动态生成
const SCENARIO_CONFIG = {
    scenarios: {
        'qiankaiyc': {
            name: 'qiankaiyc',
            description: '千开YC港口作业场景',
            topics: [] // 动态生成
        },
        'qiankaiqc': {
            name: 'qiankaiqc',
            description: '千开QC岸桥作业场景',
            topics: [] // 动态生成
        },
        'qiankaisc': {
            name: 'qiankaisc',
            description: '千开SC堆场作业场景',
            topics: [] // 动态生成
        }
    },
    
    // 数据类型到场景的映射
    dataTypeToScenario: {
        'CSP_TIME': 'qiankaiyc',
        'DEV_LOC': 'qiankaiyc', 
        'DCV_BASE': 'qiankaiyc',
        'QC_TIME': 'qiankaiqc',
        'QC_LOC': 'qiankaiqc',
        'QC_BASE': 'qiankaiqc',
        'SC_TIME': 'qiankaisc',
        'SC_LOC': 'qiankaisc',
        'SC_BASE': 'qiankaisc'
    },
    
    // 数据类型到topic ID的映射
    dataTypeToTopicId: {
        'CSP_TIME': 'csp_time',
        'DEV_LOC': 'dev_loc',
        'DCV_BASE': 'dcv_base',
        'QC_TIME': 'qc_time',
        'QC_LOC': 'qc_loc',
        'QC_BASE': 'qc_base',
        'SC_TIME': 'sc_time',
        'SC_LOC': 'sc_loc',
        'SC_BASE': 'sc_base'
    },
    
    // 默认场景
    defaultScenario: 'qiankaiyc'
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SCENARIO_CONFIG;
} else {
    window.SCENARIO_CONFIG = SCENARIO_CONFIG;
}




