class UniversalScenarioClient {
    constructor() {
        this.ws = null;
        this.root = null;
        this.protobufReady = false;
        
        // 加载场景配置 - 添加安全检查
        if (typeof SCENARIO_CONFIG === 'undefined') {
            console.error('SCENARIO_CONFIG not found! Please ensure scenario-config.js is loaded.');
            this.config = {
                scenarios: {
                    'qiankaiyc': {
                        name: 'qiankaiyc',
                        topics: [
                            { id: 'csp_time', displayName: 'csp_time', dataType: 'csp_time', maxCount: 140, fields: [] },
                            { id: 'dev_loc', displayName: 'dev_loc', dataType: 'dev_loc', maxCount: 140, fields: [] },
                            { id: 'dcv_base', displayName: 'dcv_base', dataType: 'dcv_base', maxCount: 140, fields: [] }
                        ]
                    }
                },
                defaultScenario: 'qiankaiyc'
            };
        } else {
            this.config = SCENARIO_CONFIG;
        }
        
        this.currentScenario = this.config.defaultScenario;
        
        // 通用数据存储 - 按场景和topic组织
        this.scenarioData = {};
        this.topicStats = {};
        
        // 初始化所有场景的数据存储
        this.initializeScenarios();
        
        // UI状态
        this.selectedTopic = null;
        this.modalOpen = false;
        this.modalUpdateInterval = null;
        
        // 统计信息
        this.stats = {
            total: 0,
            entry: 0,
            exit: 0,
            lastMessageSize: 0
        };
        
        this.initProtobuf().then(() => {
            this.initUI();
        }).catch(error => {
        });
    }

    // 初始化所有场景的数据存储
    initializeScenarios() {
        Object.keys(this.config.scenarios).forEach(scenarioId => {
            this.scenarioData[scenarioId] = {};
            this.topicStats[scenarioId] = {};
            
            const scenario = this.config.scenarios[scenarioId];
            scenario.topics.forEach(topic => {
                this.scenarioData[scenarioId][topic.id] = [];
                this.topicStats[scenarioId][topic.id] = {
                    count: 0,
                    lastTime: null,
                    maxCount: topic.maxCount || 140
                };
            });
        });
    }

    // 获取当前场景配置
    getCurrentScenario() {
        return this.config.scenarios[this.currentScenario];
    }

    // 获取当前场景的topics
    getCurrentTopics() {
        return this.getCurrentScenario().topics;
    }

    // 更新topic统计
    updateTopicStats(topicId, insertDate = null) {
        if (!this.topicStats[this.currentScenario] || !this.topicStats[this.currentScenario][topicId]) {
            return;
        }
        
        const stats = this.topicStats[this.currentScenario][topicId];
        
        // 移除csp_time的特殊处理，所有topic都正常累计
        stats.count++;
        
        // 使用推送数据的insertDate，如果没有则使用当前时间
        if (insertDate) {
            stats.lastTime = insertDate;
        } else {
            stats.lastTime = new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
            }).replace(/\//g, '-') + ' ' + new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        }
    }

    // 通用数据处理器
    handleTopicData(topicId, data) {
        const scenario = this.getCurrentScenario();
        const topic = scenario.topics.find(t => t.id === topicId);
        
        if (!topic) {
            return;
        }

        // 更新统计，传入insertDate
        this.updateTopicStats(topicId, data.insertDate);
        
        // 存储数据
        const topicData = this.scenarioData[this.currentScenario][topicId];
        topicData.unshift(data);
        
        // 限制数据量
        if (topicData.length > topic.maxCount) {
            this.scenarioData[this.currentScenario][topicId] = topicData.slice(0, topic.maxCount);
        }
        
        // 更新UI
        this.updateTopicDisplay(topicId);
        this.updateDataDisplay(topicId);
        this.updateStats();
    }

    // 通用数据显示更新
    updateDataDisplay(topicId) {
        const topic = this.getCurrentTopics().find(t => t.id === topicId);
        if (!topic) return;

        const containerId = this.getContainerIdForTopic(topicId);
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        const data = this.scenarioData[this.currentScenario][topicId] || [];
        
        data.slice(0, 5).forEach((item, index) => {
            const div = document.createElement('div');
            div.className = index === 0 ? 'data-item new-item' : 'data-item';
            div.innerHTML = this.formatDataItem(topic, item);
            container.appendChild(div);
        });
    }

    // 根据topic配置格式化数据显示
    formatDataItem(topic, item) {
        let html = `<strong>${topic.dataType}推送时间:</strong> ${item.insertDate}<br>`;
        
        if (item.statusList && Array.isArray(item.statusList)) {
            html += item.statusList.map(status => {
                let statusHtml = `<strong>设备ID:</strong> ${status.id}<br>`;
                
                // 动态处理后端返回的字段，而不是使用固定配置
                if (status.fields && Array.isArray(status.fields)) {
                    status.fields.forEach(field => {
                        const displayName = this.getFieldDisplayName(field.key) || field.key;
                        const value = this.getFieldValue(field);
                        if (value !== undefined && value !== null) {
                            statusHtml += `<strong>${displayName}:</strong> ${value}<br>`;
                        }
                    });
                } else {
                    // 如果没有fields数组，直接显示status对象的所有属性
                    Object.keys(status).forEach(key => {
                        if (key !== 'id') {
                            const displayName = this.getFieldDisplayName(key) || key;
                            const value = status[key];
                            if (value !== undefined && value !== null) {
                                statusHtml += `<strong>${displayName}:</strong> ${this.formatValue(key, value)}<br>`;
                            }
                        }
                    });
                }
                
                return statusHtml;
            }).join('<hr>');
        }
        
        return html;
    }

    // 新增：从字段对象中提取值
    getFieldValue(field) {
        switch (field.type) {
            case 0: // STRING
                return field.stringValue;
            case 1: // DOUBLE
                return typeof field.doubleValue === 'number' ? field.doubleValue.toFixed(2) : field.doubleValue;
            case 2: // INT32
            case 3: // INT64
                return field.intValue;
            case 4: // BOOL
                return field.boolValue;
            case 5: // BYTES
                return field.bytesValue;
            default:
                return field.stringValue || field.doubleValue || field.intValue || field.boolValue;
        }
    }

    // 新增：获取字段的显示名称
    getFieldDisplayName(key) {
        const displayNameMap = {
            'equipmentId': '设备ID',
            'workingArea': '作业区域', 
            'stackHeight': '堆垛高度',
            'efficiency': '作业效率',
            'ltmX': 'X坐标',
            'ltmY': 'Y坐标',
            'rawHeadTo': '朝向',
            'trackList': '轨迹点',
            'fuelType': '燃料类型',
            'fuelRemainingPercentage': '燃料剩余',
            'workStatus': '工作状态',
            'energy': '能量',
            'jobMode': '作业模式',
            'current': '当前时间',
            'shiftInfo': '班次信息',
            'craneId': '岸桥ID',
            'workingBay': '作业贝位',
            'loadCapacity': '负载能力',
            'operationMode': '操作模式',
            'loadStatus': '负载状态',
            'containerCount': '集装箱数量',
            'height': '高度',
            'trolleyPosition': '小车位置',
            'blockId': '堆场块号',
            'bayPosition': '贝位',
            'rowPosition': '排位',
            'tierPosition': '层位'
        };
        return displayNameMap[key];
    }

    // 新增：格式化值显示
    formatValue(key, value) {
        if (typeof value === 'number') {
            // 如果是小数，保留2位小数
            return value % 1 !== 0 ? value.toFixed(2) : value;
        }
        if (Array.isArray(value)) {
            return `${value.length} 项`;
        }
        return value;
    }

    // 获取topic对应的容器ID
    getContainerIdForTopic(topicId) {
        // 根据topic类型确定容器，支持所有场景
        if (topicId.includes('time')) return 'cspTimeData';
        if (topicId.includes('loc')) return 'devLocData';
        if (topicId.includes('base')) return 'dcvBaseData';
        
        // 如果没有匹配的容器，使用第一个可用的容器
        const containers = ['cspTimeData', 'devLocData', 'dcvBaseData'];
        const topics = this.getCurrentTopics();
        const topicIndex = topics.findIndex(t => t.id === topicId);
        
        if (topicIndex >= 0 && topicIndex < containers.length) {
            return containers[topicIndex];
        }
        
        return 'cspTimeData'; // 默认
    }

    // 更新场景显示
    updateScenarioDisplay() {
        const select = document.getElementById('scenarioSelect');
        if (select) {
            select.value = this.currentScenario;
        }
        
        // 更新页面标题
        const title = document.querySelector('h1');
        if (title) {
            const scenarioName = this.getCurrentScenario().name;
            title.textContent = `孪生数据实时监控 - ${scenarioName}`;
        }
        
        // 更新数据区域标题
        this.updateDataSectionTitles();
    }

    // 更新数据区域标题
    updateDataSectionTitles() {
        const topics = this.getCurrentTopics();
        const titleElements = [
            document.getElementById('dataSection1Title'),
            document.getElementById('dataSection2Title'),
            document.getElementById('dataSection3Title')
        ];
        
        topics.forEach((topic, index) => {
            if (titleElements[index]) {
                titleElements[index].textContent = topic.dataType; // 显示dataType而不是displayName
            }
        });
    }

    // 切换场景
    switchScenario(scenarioId) {
        if (!this.config.scenarios[scenarioId]) {
            return;
        }

        this.currentScenario = scenarioId;
        
        // 重置UI状态
        this.selectedTopic = null;
        if (this.modalOpen) {
            this.closeModal();
        }
        
        // 重新创建topic列表
        this.createTopicListItems();
        
        // 更新界面
        this.updateScenarioDisplay();
        this.updateAllDataDisplays();
        this.updateStats();
    }

    // 更新所有topic显示
    updateAllTopicDisplays() {
        const topics = this.getCurrentTopics();
        topics.forEach(topic => {
            this.updateTopicDisplay(topic.id);
        });
    }

    // 更新所有数据显示
    updateAllDataDisplays() {
        const topics = this.getCurrentTopics();
        topics.forEach(topic => {
            this.updateDataDisplay(topic.id);
        });
    }

    // 初始化UI
    initUI() {
        this.initScenarioSelector();
        this.initTopicDisplay();
        this.initKeyboardListener();
        this.updateScenarioDisplay();
    }

    // 初始化场景选择器
    initScenarioSelector() {
        const select = document.getElementById('scenarioSelect');
        if (!select) return;

        // 清空现有选项
        select.innerHTML = '';
        
        // 添加所有场景选项
        Object.entries(this.config.scenarios).forEach(([id, scenario]) => {
            const option = document.createElement('option');
            option.value = id;
            option.textContent = scenario.name;
            select.appendChild(option);
        });
        
        select.value = this.currentScenario;
    }

    // 处理通用数据消息
    handleCommonData(commonData) {
        
        // 查找对应的topic配置
        const topics = this.getCurrentTopics();
        const topic = topics.find(t => {
            // 通过数据类型映射匹配
            return this.config.dataTypeMapping[t.dataType] === commonData.dataType;
        });
        
        if (topic) {
            // 转换为标准格式
            const convertedData = this.convertCommonToStandard(commonData);
            this.handleTopicData(topic.id, convertedData);
        } else {
        }
    }

    // 将通用格式转换为标准格式
    convertCommonToStandard(commonData) {
        return {
            insertDate: commonData.insertDate,
            statusList: commonData.statusList.map(status => {
                const result = { id: status.id };
                
                if (status.fields) {
                    status.fields.forEach(field => {
                        switch (field.type) {
                            case 0: // STRING
                                result[field.key] = field.stringValue;
                                break;
                            case 1: // DOUBLE
                                result[field.key] = field.doubleValue;
                                break;
                            case 2: // INT32
                            case 3: // INT64
                                result[field.key] = field.intValue;
                                break;
                            case 4: // BOOL
                                result[field.key] = field.boolValue;
                                break;
                            case 5: // BYTES
                                result[field.key] = field.bytesValue;
                                break;
                            default:
                                console.warn('Unknown field type:', field.type);
                        }
                    });
                }
                
                return result;
            })
        };
    }

    async initProtobuf() {
        try {
            // 简化路径处理，避免使用import.meta
            const protoPath = '../../proto/cargo.proto';
            
            // 尝试加载完整的proto文件
            try {
                this.root = await protobuf.load(protoPath);
                this.WebSocketMessage = this.root.lookupType('cargo.WebSocketMessage');
                this.MessageType = this.root.lookupEnum('cargo.MessageType');
                this.CommonData = this.root.lookupType('cargo.CommonData');
                this.Message = this.root.lookupType('cargo.Message'); // 添加Message类型
            } catch (loadError) {
                // 如果加载失败，使用内联定义
                const protoDefinition = {
                    nested: {
                        cargo: {
                            nested: {
                                MessageType: {
                                    values: {
                                        UNKNOWN: 0,
                                        CARGO_DATA: 1,
                                        CARGO_BATCH: 2,
                                        COMMON_DATA: 3,
                                        MESSAGE: 4
                                    }
                                },
                                WebSocketMessage: {
                                    fields: {
                                        type: { type: "MessageType", id: 1 },
                                        payload: { type: "bytes", id: 2 },
                                        timestamp: { type: "int64", id: 3 },
                                        messageId: { type: "string", id: 4 }
                                    }
                                },
                                Message: {
                                    fields: {
                                        insertDate: { type: "string", id: 1 },
                                        payload: { keyType: "string", type: "string", id: 2 }
                                    }
                                },
                                CommonData: {
                                    fields: {
                                        dataType: { type: "string", id: 1 },
                                        insertDate: { type: "string", id: 2 },
                                        statusList: { rule: "repeated", type: "CommonStatus", id: 3 }
                                    }
                                },
                                CommonStatus: {
                                    fields: {
                                        id: { type: "string", id: 1 },
                                        fields: { rule: "repeated", type: "DataField", id: 2 }
                                    }
                                },
                                DataField: {
                                    fields: {
                                        key: { type: "string", id: 1 },
                                        type: { type: "int32", id: 2 },
                                        stringValue: { type: "string", id: 3 },
                                        doubleValue: { type: "double", id: 4 },
                                        intValue: { type: "int32", id: 5 }
                                    }
                                }
                            }
                        }
                    }
                };

                this.root = protobuf.Root.fromJSON(protoDefinition);
                this.WebSocketMessage = this.root.lookupType('cargo.WebSocketMessage');
                this.MessageType = this.root.lookupEnum('cargo.MessageType');
                this.CommonData = this.root.lookupType('cargo.CommonData');
                this.Message = this.root.lookupType('cargo.Message');
            }
            
            this.protobufReady = true;
        } catch (error) {
            this.protobufReady = false;
        }
    }

    async connect() {
        // 等待protobuf初始化完成
        while (!this.protobufReady) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            return;
        }

        // 连接真实服务器地址
        try {
            this.ws = new WebSocket(`ws://localhost:8080/`);
            this.ws.binaryType = 'arraybuffer';
            
            // 添加连接状态监听
            this.ws.addEventListener('open', () => {
            });
            
            this.ws.addEventListener('error', (error) => {
           
            });
            
        } catch (error) {
        }
        
        if (!this.ws) {
            this.updateStatus(false);
            return;
        }

        let connected = false;
        
        try {
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    if (this.ws) {
                        this.ws.close();
                    }
                    reject(new Error('Connection timeout'));
                }, 10000);

                this.ws.onopen = () => {
                    clearTimeout(timeout);
                    console.log('✓ Connected to WebSocket server successfully');
                    this.updateStatus(true);
                    this.startHeartbeat();
                    connected = true;
                    
                    // 移除csp_time模拟，完全依赖后端数据
                    // this.startCspTimeSimulation();
                    
                    resolve();
                };

                this.ws.onerror = (error) => {
                    clearTimeout(timeout);
                    reject(new Error('Connection failed - check if server is running and accessible'));
                };

                this.ws.onclose = (event) => {
                    clearTimeout(timeout);
                    if (!connected) {
                        reject(new Error(`Connection closed: ${event.reason || 'Unknown reason'}`));
                    }
                };
            });
            
            // 设置消息处理器
            if (connected) {
                this.ws.onmessage = (event) => {
                    this.handleMessage(event);
                };

                this.ws.onclose = () => {
                    this.updateStatus(false);
                    this.stopHeartbeat();
                };

                this.ws.onerror = (error) => {
                    this.updateStatus(false);
                };
            }
        } catch (error) {
            this.updateStatus(false);
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.stopHeartbeat();
        
        // 停止csp_time模拟定时器
        if (this.cspTimeInterval) {
            clearInterval(this.cspTimeInterval);
            this.cspTimeInterval = null;
            console.log('停止csp_time模拟定时器');
        }
        
        this.updateStatus(false);
        console.log('Disconnected from WebSocket server');
    }

    handleMessage(event) {
        console.log('=== 收到后端推送数据 ===');
        console.log('原始数据类型:', typeof event.data);
        console.log('数据大小:', event.data.byteLength, 'bytes');
        
        // 显示原始消息到页面
        this.displayRawMessage(event.data);
        
        try {
            const buffer = new Uint8Array(event.data);
            console.log('二进制数据 (前50字节):', Array.from(buffer.slice(0, 50)).map(b => b.toString(16).padStart(2, '0')).join(' '));
            
            let decoded = false;
            
            // 1. 尝试解码为WebSocketMessage格式
            if (this.WebSocketMessage) {
                try {
                    const wsMessage = this.WebSocketMessage.decode(buffer);
                    console.log('✓ WebSocketMessage 解码成功:');
                    console.log('  - 消息类型:', wsMessage.type);
                    console.log('  - 时间戳:', wsMessage.timestamp);
                    console.log('  - 消息ID:', wsMessage.messageId);
                    console.log('  - Payload大小:', wsMessage.payload.length);
                    
                    // 根据消息类型处理payload
                    if (wsMessage.type === this.MessageType.values.COMMON_DATA) {
                        const commonData = this.CommonData.decode(wsMessage.payload);
                        console.log('✓ CommonData 解码成功:', JSON.stringify(commonData, null, 2));
                        this.handleCommonData(commonData);
                    } else if (wsMessage.type === this.MessageType.values.MESSAGE) {
                        const messageData = this.Message.decode(wsMessage.payload);
                        console.log('✓ Message 解码成功:', JSON.stringify(messageData, null, 2));
                        this.handleMessageData(messageData);
                    } else {
                        console.log('未知消息类型:', wsMessage.type);
                    }
                    
                    decoded = true;
                    this.stats.lastMessageSize = buffer.length;
                    this.updateStats();
                    return;
                } catch (protobufError) {
                    console.log('✗ WebSocketMessage 解码失败:', protobufError.message);
                }
            }
            
            // 2. 尝试直接解码为Message格式
            if (this.Message && !decoded) {
                try {
                    const messageData = this.Message.decode(buffer);
                    console.log('✓ 直接 Message 解码成功:', JSON.stringify(messageData, null, 2));
                    this.handleMessageData(messageData);
                    decoded = true;
                    this.stats.lastMessageSize = buffer.length;
                    this.updateStats();
                    return;
                } catch (protobufError) {
                    console.log('✗ Message 解码失败:', protobufError.message);
                }
            }
            
            // 3. 尝试直接解码为CommonData
            if (this.CommonData && !decoded) {
                try {
                    const commonData = this.CommonData.decode(buffer);
                    console.log('✓ 直接 CommonData 解码成功:', JSON.stringify(commonData, null, 2));
                    this.handleCommonData(commonData);
                    decoded = true;
                    this.stats.lastMessageSize = buffer.length;
                    this.updateStats();
                    return;
                } catch (protobufError) {
                    console.log('✗ CommonData 解码失败:', protobufError.message);
                }
            }
            
            // 4. 如果都失败了，尝试作为文本处理
            if (!decoded) {
                console.log('所有 protobuf 解码都失败，尝试文本解码...');
                try {
                    const textData = new TextDecoder('utf-8').decode(buffer);
                    console.log('文本内容:', textData);
                    
                    const jsonData = JSON.parse(textData);
                    console.log('JSON 解析成功:', jsonData);
                } catch (textError) {
                    console.log('文本/JSON 解码也失败:', textError.message);
                    console.log('原始字节数据:', Array.from(buffer));
                }
            }
            
        } catch (error) {
            console.error('处理消息时发生错误:', error);
        }
        
        console.log('=== 数据处理完成 ===\n');
    }

    // 新增：处理Java后端消息的方法
    handleJavaMessage(message) {
        
        // 将Java消息转换为前端期望的格式
        const processedData = {
            insertDate: message.insertDate,
            dataType: 'java_kafka_data', // 标识这是来自Java的Kafka数据
            statusList: [this.convertJavaPayloadToStatus(message.payload)]
        };
        
        // 找到匹配的topic或使用默认topic
        const currentTopics = this.getCurrentTopics();
        let targetTopic = currentTopics.find(t => t.dataType === 'java_kafka_data');
        
        if (!targetTopic) {
            // 如果没有匹配的topic，使用第一个topic
            targetTopic = currentTopics[0];
        }
        
        if (targetTopic) {
            this.handleTopicData(targetTopic.id, processedData);
        }
    }

    // 将Java payload转换为status格式
    convertJavaPayloadToStatus(payloadMap) {
        const status = {
            id: `java_device_${Math.random().toString(36).substr(2, 6)}`
        };

        // 将Java的Map<String,String>转换为前端格式
        if (payloadMap) {
            Object.keys(payloadMap).forEach(key => {
                status[key] = payloadMap[key];
            });
        }

        return status;
    }

    handleCargoData(cargoData) {
        // 保持原有的cargo数据处理逻辑
        if (!this.cargoData) {
            this.cargoData = [];
        }
        
        this.cargoData.unshift(cargoData);
        
        if (this.cargoData.length > 100) {
            this.cargoData = this.cargoData.slice(0, 100);
        }
        
        this.updateTable();
        this.updateStats();
    }

    handleCargoBatch(batch) {
        // 保持原有的批量数据处理逻辑
        if (!this.cargoData) {
            this.cargoData = [];
        }
        
        batch.items.forEach(item => {
            this.cargoData.unshift(item);
        });
        
        if (this.cargoData.length > 100) {
            this.cargoData = this.cargoData.slice(0, 100);
        }
        
        this.updateTable();
        this.updateStats();
    }

    updateTable() {
        const tbody = document.getElementById('dataTableBody');
        if (!tbody || !this.cargoData) return;
        
        tbody.innerHTML = '';
        
        this.cargoData.forEach((item, index) => {
            const row = tbody.insertRow();
            if (index === 0) {
                row.className = 'new-row';
            }
            
            row.innerHTML = `
                <td>${item.id}</td>
                <td>${item.timestamp}</td>
                <td>${item.code}</td>
                <td>${item.entryExitType}</td>
                <td>${item.entryExitIndicator}</td>
                <td>${item.cargoName}</td>
                <td>${item.originCountry}</td>
                <td>${item.declaredQuantity} ${item.unit}</td>
                <td>${item.checkPoint}</td>
                <td>${item.vin}</td>
            `;
        });
    }

    // 更新统计显示
    updateStats() {
        // 更新cargo统计
        if (this.cargoData) {
            this.stats.total = this.cargoData.length;
            this.stats.entry = this.cargoData.filter(item => item.entryExitIndicator === '入').length;
            this.stats.exit = this.cargoData.filter(item => item.entryExitIndicator === '出').length;
            
            const totalCountEl = document.getElementById('totalCount');
            const entryCountEl = document.getElementById('entryCount');
            const exitCountEl = document.getElementById('exitCount');
            
            if (totalCountEl) totalCountEl.textContent = this.stats.total;
            if (entryCountEl) entryCountEl.textContent = this.stats.entry;
            if (exitCountEl) exitCountEl.textContent = this.stats.exit;
        }
        
        // 计算当前场景的总统计
        const currentStats = this.topicStats[this.currentScenario] || {};
        const topics = this.getCurrentTopics();
        
        // 更新各个topic的统计显示
        topics.forEach((topic, index) => {
            const stats = currentStats[topic.id] || { count: 0 };
            const countElements = [
                document.getElementById('cspTimeCount'),
                document.getElementById('devLocCount'), 
                document.getElementById('dcvBaseCount')
            ];
            
            if (countElements[index]) {
                countElements[index].textContent = stats.count;
            }
        });
        
        // 更新消息大小
        const messageSizeEl = document.getElementById('messageSize');
        if (messageSizeEl) {
            messageSizeEl.textContent = `${this.stats.lastMessageSize} bytes`;
        }
    }

    updateStatus(connected) {
        const statusEl = document.getElementById('status');
        if (connected) {
            statusEl.textContent = '连接状态: 已连接';
            statusEl.className = 'status connected';
        } else {
            statusEl.textContent = '连接状态: 未连接';
            statusEl.className = 'status disconnected';
        }
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                const wsMsg = this.WebSocketMessage.create({
                    type: this.MessageType.values.HEARTBEAT,
                    payload: new Uint8Array(),
                    timestamp: Date.now(),
                    messageId: `heartbeat_${Date.now()}`
                });
                
                const buffer = this.WebSocketMessage.encode(wsMsg).finish();
                this.ws.send(buffer);
            }
        }, 30000);
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    clearData() {
        // 清空cargo数据
        this.cargoData = [];
        
        // 清空所有场景数据
        Object.keys(this.scenarioData).forEach(scenarioId => {
            Object.keys(this.scenarioData[scenarioId]).forEach(topicId => {
                this.scenarioData[scenarioId][topicId] = [];
            });
        });
        
        // 重置topic统计
        Object.keys(this.topicStats).forEach(scenarioId => {
            Object.keys(this.topicStats[scenarioId]).forEach(topicId => {
                this.topicStats[scenarioId][topicId].count = 0;
                this.topicStats[scenarioId][topicId].lastTime = null;
            });
        });
        
        // 停止csp_time模拟定时器
        if (this.cspTimeInterval) {
            clearInterval(this.cspTimeInterval);
            this.cspTimeInterval = null;
        }
        
        this.updateTable();
        this.updateAllDataDisplays();
        this.updateTopicDisplay();
        this.updateStats();
    }

    exportData() {
        const allData = {
            cargoData: this.cargoData || [],
            scenarioData: this.scenarioData,
            currentScenario: this.currentScenario,
            exportTime: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(allData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `scenario_data_${new Date().toISOString().slice(0, 10)}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    // 初始化Topic显示
    initTopicDisplay() {
        this.createTopicListItems();
        // 每秒更新一次显示
        setInterval(() => {
            this.updateTopicDisplayProgress();
        }, 1000);
    }

    // 创建topic列表项
    createTopicListItems() {
        const container = document.getElementById('topicList');
        if (!container) {
            console.error('Topic list container not found');
            return;
        }
        
        container.innerHTML = '';
        
        this.getCurrentTopics().forEach(topic => {
            const div = document.createElement('div');
            div.className = 'topic-item';
            div.setAttribute('data-topic', topic.id);
            
            div.innerHTML = `
                <div class="topic-progress">
                    <span class="progress-bars"></span>
                </div>
                <div class="topic-name" data-topic="${topic.id}">${topic.dataType}</div>
                <div class="topic-time">未推送</div>
            `;
            
            // 添加点击事件
            const topicNameDiv = div.querySelector('.topic-name');
            topicNameDiv.style.cursor = 'pointer';
            topicNameDiv.addEventListener('click', () => {
                this.selectTopic(topic.id);
            });
            
            container.appendChild(div);
        });
        
    }

    // 更新topic显示进度条
    updateTopicDisplayProgress() {
        const container = document.getElementById('topicList');
        if (!container) return;

        this.getCurrentTopics().forEach(topic => {
            const topicItem = container.querySelector(`[data-topic="${topic.id}"]`);
            if (!topicItem) return;
            
            const stats = this.topicStats[this.currentScenario][topic.id];
            if (!stats) return;
            
            // 移除csp_time的特殊处理，所有topic都显示正常的累计进度条
            const progressBars = Array.from({ length: stats.maxCount }, (_, i) => {
                return i < stats.count ? '|' : ' ';
            }).reverse().join('');
            
            const timeStr = stats.lastTime || '未推送';
            
            // 更新进度条和时间
            const progressBarsEl = topicItem.querySelector('.progress-bars');
            const timeEl = topicItem.querySelector('.topic-time');
            const nameEl = topicItem.querySelector('.topic-name');
            
            if (progressBarsEl) progressBarsEl.textContent = progressBars;
            if (timeEl) timeEl.textContent = timeStr;
            
            // 更新选中状态
            if (nameEl) {
                if (this.selectedTopic === topic.id) {
                    nameEl.classList.add('selected');
                } else {
                    nameEl.classList.remove('selected');
                }
            }
        });
    }

    // 更新topic显示
    updateTopicDisplay(topicId) {
        // 如果没有指定topicId，重新创建所有topic项
        if (!topicId) {
            this.createTopicListItems();
            return;
        }
        
        const topic = this.getCurrentTopics().find(t => t.id === topicId);
        if (!topic) return;
        
        const stats = this.topicStats[this.currentScenario][topicId];
        if (!stats) return;
        
        // 更新topic列表显示
        const container = document.getElementById('topicList');
        if (!container) return;
        
        // 检查topic元素是否存在，不存在才创建
        let topicElement = container.querySelector(`[data-topic="${topicId}"]`);
        if (!topicElement) {
            // 只创建这一个topic元素，而不是重建整个列表
            this.createSingleTopicItem(topicId, topic);
            return;
        }
        
        // 更新现有元素的时间显示
        const timeElement = topicElement.querySelector('.topic-time');
        if (timeElement && stats.lastTime) {
            timeElement.textContent = stats.lastTime;
            topicElement.classList.add('topic-new');
            setTimeout(() => topicElement.classList.remove('topic-new'), 1000);
        }
    }

    // 格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        const ms = String(date.getMilliseconds()).padStart(3, '0');
        
        return `[${month}-${day} ${hours}:${minutes}:${seconds}-${ms.substring(0,2)}:00]`;
    }

    // 更新Topic计数
    updateTopicCount(topicName) {
        if (this.topics[topicName]) {
            this.topics[topicName].count++;
            this.topics[topicName].lastTime = Date.now();
            
            // 添加闪烁效果
            setTimeout(() => {
                const items = document.querySelectorAll('.topic-item');
                const index = Object.keys(this.topics).indexOf(topicName);
                if (items[index]) {
                    items[index].classList.add('topic-new');
                    setTimeout(() => {
                        items[index].classList.remove('topic-new');
                    }, 100);
                }
            }, 100);
        }
    }

    // 初始化键盘监听
    initKeyboardListener() {
        document.addEventListener('keydown', (event) => {
            // ESC键关闭弹框
            if (event.key === 'Escape' && this.modalOpen) {
                this.closeModal();
            }
            
            // Ctrl+Shift+1 打开/切换弹框
            if (event.ctrlKey && event.shiftKey && event.key === '!') {
                event.preventDefault();
                this.toggleModal();
            }
        });
    }

    // 切换弹框显示
    toggleModal() {
        if (this.modalOpen) {
            this.closeModal();
        } else {
            this.openModal();
        }
    }

    // 打开弹框
    openModal() {
        if (!this.selectedTopic) {
            alert('请先点击选择一个topic');
            return;
        }

        const modal = document.getElementById('dataModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalData = document.getElementById('modalData');

        if (!modal || !modalTitle || !modalData) {
            console.error('Modal elements not found');
            return;
        }

        const topic = this.getCurrentTopics().find(t => t.id === this.selectedTopic);
        const topicDataType = topic ? topic.dataType : this.selectedTopic.toUpperCase();
        modalTitle.textContent = `${this.getCurrentScenario().name} - ${topicDataType}`;
        
        // 立即更新一次数据
        this.updateModalData();

        modal.style.display = 'block';
        this.modalOpen = true;
        
        // 启动实时更新定时器，每500ms更新一次
        this.modalUpdateInterval = setInterval(() => {
            this.updateModalData();
        }, 500);
        
    }

    // 关闭弹框
    closeModal() {
        const modal = document.getElementById('dataModal');
        if (modal) {
            modal.style.display = 'none';
        }
        
        this.modalOpen = false;
        
        if (this.modalUpdateInterval) {
            clearInterval(this.modalUpdateInterval);
            this.modalUpdateInterval = null;
        }
        
    }

    // 更新弹框数据
    updateModalData() {
        if (!this.modalOpen || !this.selectedTopic) {
            return;
        }

        const modalData = document.getElementById('modalData');
        if (!modalData) return;

        const data = this.scenarioData[this.currentScenario][this.selectedTopic] || [];
        const latestData = data[0];
        
        if (latestData) {
            const displayData = {
                '数据更新时间': new Date().toLocaleString(),
                '数据总数': data.length,
                '最新数据': latestData
            };
            modalData.textContent = JSON.stringify(latestData, null, 2);
        } else {
            modalData.textContent = `暂无 ${this.selectedTopic} 数据\n更新时间: ${new Date().toLocaleString()}`;
        }
    }

    // 选择topic
    selectTopic(topicName) {
        
        this.selectedTopic = topicName;
        
        // 更新UI显示选中状态
        document.querySelectorAll('.topic-name').forEach(el => {
            el.classList.remove('selected');
        });
        
        // 找到对应的topic元素并添加选中样式
        const topicElement = document.querySelector(`[data-topic="${topicName}"] .topic-name`);
        if (topicElement) {
            topicElement.classList.add('selected');
        }
    }

    // 修改测试方法，接收数据后自动弹框显示
    testWithBase64Data(base64String) {
        
        try {
            // Convert base64 to ArrayBuffer
            const binaryString = atob(base64String);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            
            
            // Create a mock event object
            const mockEvent = {
                data: bytes.buffer
            };
            
            // Process through your existing message handler
            this.handleMessage(mockEvent);
            
            // 检查数据是否被处理
            
            // 自动打开弹框显示数据
            setTimeout(() => {
                // 如果有选中的topic，直接打开弹框
                if (this.selectedTopic) {
                    this.openModal();
                } else {
                    // 如果没有选中topic，选择第一个有数据的topic
                    const topics = this.getCurrentTopics();
                    for (const topic of topics) {
                        const data = this.scenarioData[this.currentScenario][topic.id];
                        if (data && data.length > 0) {
                            this.selectTopic(topic.id);
                            this.openModal();
                            break;
                        }
                    }
                }
            }, 500); // 增加延迟时间
            
        } catch (error) {
        }
    }

    // 生成一个简单的测试数据
    generateTestData() {
        // 直接返回预制的base64数据，避免编码错误
        const testBase64 = 'CAMSHgoIY3NwX3RpbWUSEgoMdGVzdF9kZXZpY2VfMDAxGhQKB2N1cnJlbnQSCTIwMjQtMDEtMDE=';
        return testBase64;
    }

    // 新增方法：显示原始消息
    displayRawMessage(data) {
        const messageDiv = document.getElementById('messageContent');
        if (!messageDiv) return;
        
        const timestamp = new Date().toLocaleString();
        const buffer = new Uint8Array(data);
        
        // 格式化显示消息
        const messageInfo = `
=== 消息推送 [${timestamp}] ===
消息大小: ${buffer.byteLength} bytes
原始数据 (前100字节): ${Array.from(buffer.slice(0, 100)).map(b => b.toString(16).padStart(2, '0')).join(' ')}

尝试解码为文本:
${this.tryDecodeAsText(buffer)}

尝试解析为JSON:
${this.tryParseAsJSON(buffer)}

---
`;
        
        // 添加到消息展示区域（最新消息在顶部）
        messageDiv.textContent = messageInfo + messageDiv.textContent;
        
        // 限制显示的消息数量，避免页面过长
        const lines = messageDiv.textContent.split('\n');
        if (lines.length > 500) {
            messageDiv.textContent = lines.slice(0, 500).join('\n');
        }
    }

    // 尝试解码为文本
    tryDecodeAsText(buffer) {
        try {
            const textDecoder = new TextDecoder('utf-8');
            return textDecoder.decode(buffer);
        } catch (e) {
            return `无法解码为文本: ${e.message}`;
        }
    }

    // 尝试解析为JSON
    tryParseAsJSON(buffer) {
        try {
            const textDecoder = new TextDecoder('utf-8');
            const textData = textDecoder.decode(buffer);
            const jsonData = JSON.parse(textData);
            return JSON.stringify(jsonData, null, 2);
        } catch (e) {
            return `无法解析为JSON: ${e.message}`;
        }
    }

    // 添加模拟csp_time数据的方法
    simulateCspTimeData() {
        const cspTimeData = {
            insertDate: new Date().toISOString(),
            dataType: 'csp_time',
            statusList: [{
                id: 'csp_001',
                current: new Date().toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit', 
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                })
            }]
        };
        
        console.log('模拟 csp_time 数据:', cspTimeData);
        this.handleTopicData('csp_time', cspTimeData);
    }

    // 在连接成功后启动定时推送csp_time
    startCspTimeSimulation() {
        // 每5秒更新一次csp_time
        this.cspTimeInterval = setInterval(() => {
            this.simulateCspTimeData();
        }, 5000);
        
        // 立即执行一次
        this.simulateCspTimeData();
    }

    // 动态创建或更新topic配置
    createOrUpdateTopic(dataType, sampleData) {
        const scenarioId = this.config.dataTypeToScenario[dataType];
        const topicId = this.config.dataTypeToTopicId[dataType];
        
        if (!scenarioId || !topicId) {
            console.warn(`未知的数据类型: ${dataType}`);
            return null;
        }
        
        const scenario = this.config.scenarios[scenarioId];
        if (!scenario) return null;
        
        // 查找现有topic
        let topic = scenario.topics.find(t => t.id === topicId);
        let isNewTopic = false;
        
        // 如果topic不存在，创建新的
        if (!topic) {
            isNewTopic = true;
            topic = {
                id: topicId,
                displayName: topicId,
                dataType: dataType,
                maxCount: 140,
                updateInterval: 1000,
                fields: []
            };
            scenario.topics.push(topic);
            
            // 初始化数据存储
            if (!this.scenarioData[scenarioId]) {
                this.scenarioData[scenarioId] = {};
            }
            if (!this.topicStats[scenarioId]) {
                this.topicStats[scenarioId] = {};
            }
            
            this.scenarioData[scenarioId][topicId] = [];
            this.topicStats[scenarioId][topicId] = {
                count: 0,
                lastTime: null,
                maxCount: 140
            };
            
            console.log(`创建新topic: ${topicId} (${dataType})`);
        }
        
        // 根据样本数据动态更新字段配置（不影响UI重建）
        if (sampleData && sampleData.statusList && sampleData.statusList.length > 0) {
            const sampleStatus = sampleData.statusList[0];
            if (sampleStatus.fields) {
                const newFields = sampleStatus.fields.map(field => ({
                    key: field.key,
                    type: this.getFieldTypeString(field.type),
                    displayName: this.getFieldDisplayName(field.key) || field.key
                }));
                
                // 更新字段配置（合并新字段）
                newFields.forEach(newField => {
                    const existingField = topic.fields.find(f => f.key === newField.key);
                    if (!existingField) {
                        topic.fields.push(newField);
                    }
                });
            }
        }
        
        topic.isNewTopic = isNewTopic;
        return topic;
    }

    // 获取字段类型字符串
    getFieldTypeString(typeNumber) {
        const typeMap = {
            0: 'string',
            1: 'double', 
            2: 'int',
            3: 'int64',
            4: 'bool',
            5: 'bytes'
        };
        return typeMap[typeNumber] || 'string';
    }

    // 修改handleCommonData方法，动态创建topic
    handleCommonData(data) {
        console.log('处理通用数据:', data);
        
        // 动态创建或更新topic配置
        const topic = this.createOrUpdateTopic(data.dataType, data);
        if (!topic) {
            console.warn(`无法处理数据类型: ${data.dataType}`);
            return;
        }
        
        // 确定目标场景
        const targetScenario = this.config.dataTypeToScenario[data.dataType];
        const topicId = this.config.dataTypeToTopicId[data.dataType];
        
        if (targetScenario && topicId) {
            // 如果当前场景与数据场景不匹配，可以选择切换场景或忽略
            if (this.currentScenario !== targetScenario) {
                console.log(`数据属于场景 ${targetScenario}，当前场景是 ${this.currentScenario}`);
                return; // 不处理非当前场景的数据
            }
            
            // 检查是否是新创建的topic
            const isNewTopic = !document.querySelector(`[data-topic="${topicId}"]`);
            
            // 处理数据
            this.handleTopicData(topicId, data);
            
            // 只有在新创建topic时才重建UI
            if (isNewTopic) {
                this.createTopicListItems();
            }
        }
    }

    // 新增：创建单个topic项的方法
    createSingleTopicItem(topicId, topic) {
        const container = document.getElementById('topicList');
        if (!container) return;
        
        const div = document.createElement('div');
        div.className = 'topic-item';
        div.setAttribute('data-topic', topicId);
        
        div.innerHTML = `
            <div class="topic-progress">
                <span class="progress-bars"></span>
            </div>
            <div class="topic-name" data-topic="${topicId}">${topic.dataType}</div>
            <div class="topic-time">未推送</div>
        `;
        
        // 添加点击事件
        const topicNameDiv = div.querySelector('.topic-name');
        topicNameDiv.style.cursor = 'pointer';
        topicNameDiv.addEventListener('click', () => {
            this.selectTopic(topicId);
        });
        
        container.appendChild(div);
        console.log(`创建单个topic项: ${topicId}`);
    }

    // 处理Message格式的数据
    handleMessageData(messageData) {
        console.log('处理Message数据:', messageData);
        
        // 将Message格式转换为CommonData格式，以便复用现有的显示逻辑
        const convertedData = this.convertMessageToCommonData(messageData);
        
        // 使用现有的CommonData处理逻辑
        this.handleCommonData(convertedData);
    }

    // 将Message格式转换为CommonData格式
    convertMessageToCommonData(messageData) {
        // 从payload中提取dataType，如果没有则使用默认值
        const dataType = messageData.payload.dataType || 'MESSAGE_DATA';
        
        // 将payload的键值对转换为DataField格式
        const fields = [];
        for (const [key, value] of Object.entries(messageData.payload)) {
            if (key === 'dataType') continue; // dataType已经在外层处理
            
            fields.push({
                key: key,
                type: 0, // 字符串类型
                stringValue: value
            });
        }
        
        // 创建CommonStatus
        const statusList = [{
            id: messageData.payload.deviceId || messageData.payload.id || `msg_${Math.random().toString(36).substr(2, 6)}`,
            fields: fields
        }];
        
        // 返回CommonData格式
        return {
            dataType: dataType,
            insertDate: messageData.insertDate,
            statusList: statusList
        };
    }
}

// 确保在页面加载时正确创建client对象
let client;

// 页面加载完成后初始化
window.addEventListener('load', async () => {
    try {
        // 创建客户端实例
        client = new UniversalScenarioClient();
        
        // 等待protobuf初始化
        await client.initProtobuf();
        
        // 尝试连接
        await client.connect();
        
        // 连接成功后测试数据
        if (client.ws && client.ws.readyState === WebSocket.OPEN) {
            const simpleTestData = 'CAMSHgoIY3NwX3RpbWUSEgoMdGVzdF9kZXZpY2VfMDAxGhQKB2N1cnJlbnQSCTIwMjQtMDEtMDE=';
            client.testWithBase64Data(simpleTestData);
        } else {
        }
    } catch (error) {
    }
});

// 全局函数 - 添加安全检查
async function connect() {
    if (!client) {
        console.error('Client not initialized yet');
        return;
    }
    await client.connect();
}

function disconnect() {
    if (!client) return;
    client.disconnect();
}

function clearData() {
    if (!client) return;
    client.clearData();
}

function exportData() {
    if (!client) return;
    client.exportData();
}

function openModal() {
    if (!client) return;
    client.openModal();
}

function closeModal() {
    if (!client) return;
    client.closeModal();
}

function switchScenario() {
    if (!client) return;
    const select = document.getElementById('scenarioSelect');
    if (select && select.value) {
        client.switchScenario(select.value);
    }
}

// 页面加载完成后自动连接
window.addEventListener('load', () => {
    setTimeout(async () => {
        // 确保client对象存在
        if (typeof client === 'undefined') {
            console.error('Client object is not defined. Creating new instance...');
            client = new UniversalScenarioClient();
            await client.initProtobuf();
        }
        
        if (client && client.protobufReady) {
            await connect();
            
            // 检查testWithBase64Data方法是否存在
            if (typeof client.testWithBase64Data === 'function') {
                const simpleTestData = 'CAMSHgoIY3NwX3RpbWUSEgoMdGVzdF9kZXZpY2VfMDAxGhQKB2N1cnJlbnQSCTIwMjQtMDEtMDE=';
                client.testWithBase64Data(simpleTestData);
            } else {
                console.error('testWithBase64Data method not found on client object');
            }
        } else {
            console.error('Client not ready or not initialized');
        }
    }, 2000);
}); // 增加延迟时间

// 在文件末尾添加全局函数
function connect() {
    if (client) {
        return client.connect();
    } else {
        console.error('Client not initialized');
        return Promise.reject(new Error('Client not initialized'));
    }
}

function disconnect() {
    if (client) {
        client.disconnect();
    }
}

function switchScenario() {
    if (!client) return;
    const select = document.getElementById('scenarioSelect');
    if (select && select.value) {
        client.switchScenario(select.value);
    }
}

// 确保client对象在全局可用
window.client = client;



















































