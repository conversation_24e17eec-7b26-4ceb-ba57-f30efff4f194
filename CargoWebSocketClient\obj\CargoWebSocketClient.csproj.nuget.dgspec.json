{"format": 1, "restore": {"D:\\UI_Project\\DT_Server_C\\port-meta-connector-client\\CargoWebSocketClient\\CargoWebSocketClient.csproj": {}}, "projects": {"D:\\UI_Project\\DT_Server_C\\port-meta-connector-client\\CargoWebSocketClient\\CargoWebSocketClient.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\UI_Project\\DT_Server_C\\port-meta-connector-client\\CargoWebSocketClient\\CargoWebSocketClient.csproj", "projectName": "CargoWebSocketClient", "projectPath": "D:\\UI_Project\\DT_Server_C\\port-meta-connector-client\\CargoWebSocketClient\\CargoWebSocketClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\UI_Project\\DT_Server_C\\port-meta-connector-client\\CargoWebSocketClient\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Google.Protobuf": {"target": "Package", "version": "[3.25.1, )"}, "Grpc.Tools": {"suppressParent": "All", "target": "Package", "version": "[2.59.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}